"""
街景数据加载器
用于加载和预处理街景图像数据，支持能耗预测任务
"""

import os
import json
import pandas as pd
import numpy as np
from PIL import Image
import torch
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class StreetViewDataset(Dataset):
    """街景图像数据集类"""
    
    def __init__(self, config, split='train', transform=None, max_images_per_region=None):
        """
        Args:
            config: 数据配置字典
            split: 数据集分割 ('train', 'val', 'test')
            transform: 图像变换
            max_images_per_region: 每个区域最大图像数量限制
        """
        self.config = config
        self.split = split
        self.transform = transform
        self.max_images_per_region = max_images_per_region
        
        # 加载数据
        self._load_data()
        
    def _load_data(self):
        """加载数据集"""
        try:
            # 1. 加载区域信息和能耗标签
            logger.info("加载区域信息和能耗标签...")
            with open(self.config['region2allinfo_path'], 'r', encoding='utf-8') as f:
                self.region_info = json.load(f)
            
            # 2. 加载数据集划分
            split_paths = {
                'train': self.config['train_idx_path'],
                'val': self.config['val_idx_path'],
                'test': self.config['test_idx_path']
            }
            
            split_df = pd.read_csv(split_paths[self.split])
            self.region_ids = split_df['BlockID'].tolist()
            logger.info(f"{self.split}集包含 {len(self.region_ids)} 个区域")
            
            # 3. 加载POI-街景图像映射
            logger.info("加载POI-街景图像映射...")
            with open(self.config['poi_streetview_filename_path'], 'r', encoding='utf-8') as f:
                self.poi_image_mapping = json.load(f)
            
            # 4. 构建样本列表
            self._build_sample_list()
            
        except Exception as e:
            logger.error(f"数据加载失败: {e}")
            raise
    
    def _build_sample_list(self):
        """构建样本列表"""
        self.samples = []
        missing_regions = []
        
        for region_id in self.region_ids:
            region_str = str(region_id)
            
            # 检查区域信息是否存在
            if region_str not in self.region_info:
                missing_regions.append(region_id)
                continue
            
            # 获取能耗标签
            energy_label = self.region_info[region_str].get('energy', None)
            if energy_label is None:
                missing_regions.append(region_id)
                continue
            
            # 获取街景图像列表
            image_filenames = self.poi_image_mapping.get(region_str, [])
            if not image_filenames:
                missing_regions.append(region_id)
                continue
            
            # 限制每个区域的图像数量
            if self.max_images_per_region:
                image_filenames = image_filenames[:self.max_images_per_region]
            
            # 检查图像文件是否存在
            valid_images = []
            for filename in image_filenames:
                image_path = os.path.join(self.config['streetview_image_dir'], region_str, filename)
                if os.path.exists(image_path):
                    valid_images.append(filename)
            
            if valid_images:
                self.samples.append({
                    'region_id': region_id,
                    'energy_label': energy_label,
                    'image_filenames': valid_images,
                    'region_info': self.region_info[region_str]
                })
        
        if missing_regions:
            logger.warning(f"缺失数据的区域: {missing_regions}")
        
        logger.info(f"成功构建 {len(self.samples)} 个有效样本")
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        sample = self.samples[idx]
        region_id = sample['region_id']
        energy_label = sample['energy_label']
        image_filenames = sample['image_filenames']
        
        # 加载图像
        images = []
        for filename in image_filenames:
            image_path = os.path.join(self.config['streetview_image_dir'], str(region_id), filename)
            try:
                image = Image.open(image_path).convert('RGB')
                if self.transform:
                    image = self.transform(image)
                images.append(image)
            except Exception as e:
                logger.warning(f"图像加载失败: {image_path}, 错误: {e}")
                continue
        
        if not images:
            # 如果没有有效图像，返回零张量
            if self.transform:
                dummy_image = self.transform(Image.new('RGB', (224, 224), (0, 0, 0)))
            else:
                dummy_image = torch.zeros(3, 224, 224)
            images = [dummy_image]
        
        # 如果有多张图像，可以选择平均、堆叠或选择第一张
        # 这里选择堆叠所有图像
        if len(images) == 1:
            image_tensor = images[0]
        else:
            # 堆叠图像并计算平均值
            image_tensor = torch.stack(images).mean(dim=0)
        
        return {
            'image': image_tensor,
            'energy': torch.tensor(energy_label, dtype=torch.float32),
            'region_id': region_id
        }

def get_transforms(split='train', input_size=224):
    """获取图像变换"""
    if split == 'train':
        transform = transforms.Compose([
            transforms.Resize((input_size, input_size)),
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.RandomRotation(degrees=10),
            transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
    else:
        transform = transforms.Compose([
            transforms.Resize((input_size, input_size)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
    
    return transform

def create_data_loaders(config, batch_size=16, num_workers=4, max_images_per_region=5):
    """创建数据加载器"""
    
    # 创建数据集
    train_dataset = StreetViewDataset(
        config, 
        split='train', 
        transform=get_transforms('train'),
        max_images_per_region=max_images_per_region
    )
    
    val_dataset = StreetViewDataset(
        config, 
        split='val', 
        transform=get_transforms('val'),
        max_images_per_region=max_images_per_region
    )
    
    test_dataset = StreetViewDataset(
        config, 
        split='test', 
        transform=get_transforms('test'),
        max_images_per_region=max_images_per_region
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset, 
        batch_size=batch_size, 
        shuffle=True, 
        num_workers=num_workers,
        pin_memory=True if torch.cuda.is_available() else False
    )
    
    val_loader = DataLoader(
        val_dataset, 
        batch_size=batch_size, 
        shuffle=False, 
        num_workers=num_workers,
        pin_memory=True if torch.cuda.is_available() else False
    )
    
    test_loader = DataLoader(
        test_dataset, 
        batch_size=batch_size, 
        shuffle=False, 
        num_workers=num_workers,
        pin_memory=True if torch.cuda.is_available() else False
    )
    
    logger.info(f"数据加载器创建完成:")
    logger.info(f"  训练集: {len(train_dataset)} 样本")
    logger.info(f"  验证集: {len(val_dataset)} 样本")
    logger.info(f"  测试集: {len(test_dataset)} 样本")
    
    return train_loader, val_loader, test_loader

# 配置信息
CONFIGS = {
    'shenyang': {
        'region2allinfo_path': r"D:\研二\能耗估算\666-模型对比项目\V6\data\shenyang\shenyang_region2allinfo.json",
        'train_idx_path': './data/shenyang/shenyang_zl15_train.csv',
        'val_idx_path': './data/shenyang/shenyang_zl15_valid.csv',
        'test_idx_path': './data/shenyang/shenyang_zl15_test.csv',
        'streetview_image_dir': './data/shenyang/streetview_image/Region/',
        'poi_streetview_filename_path': './data/shenyang/streetview_image/region_5_10_poi_image_filename.json',
    }
}

if __name__ == "__main__":
    # 测试数据加载器
    config = CONFIGS['shenyang']
    
    try:
        train_loader, val_loader, test_loader = create_data_loaders(
            config, 
            batch_size=8, 
            num_workers=0,  # 在Windows上建议设置为0
            max_images_per_region=3
        )
        
        # 测试一个批次
        for batch in train_loader:
            print(f"图像批次形状: {batch['image'].shape}")
            print(f"能耗标签形状: {batch['energy'].shape}")
            print(f"区域ID: {batch['region_id']}")
            break
            
        logger.info("数据加载器测试成功!")
        
    except Exception as e:
        logger.error(f"数据加载器测试失败: {e}")
