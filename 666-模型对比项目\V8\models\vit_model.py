"""
Vision Transformer (ViT) 模型 - 优化版本
论文: An Image is Worth 16x16 Words: Transformers for Image Recognition at Scale
链接: https://arxiv.org/abs/2010.11929

针对街区能耗预测任务优化，提升训练稳定性和性能
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import os
import json
from datetime import datetime
import logging
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import matplotlib.pyplot as plt
import math

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PatchEmbedding(nn.Module):
    """改进的Patch嵌入层"""
    
    def __init__(self, image_size=224, patch_size=16, in_channels=3, embed_dim=384):
        super().__init__()
        self.image_size = image_size
        self.patch_size = patch_size
        self.num_patches = (image_size // patch_size) ** 2
        self.embed_dim = embed_dim
        
        # 使用卷积进行patch嵌入
        self.projection = nn.Conv2d(
            in_channels, embed_dim, 
            kernel_size=patch_size, 
            stride=patch_size
        )
        
        # 添加归一化
        self.norm = nn.LayerNorm(embed_dim)
        
    def forward(self, x):
        B, C, H, W = x.shape
        assert H == self.image_size and W == self.image_size, \
            f"输入图像尺寸 ({H}, {W}) 与期望尺寸 ({self.image_size}, {self.image_size}) 不匹配"
        
        # (B, C, H, W) -> (B, embed_dim, H//P, W//P) -> (B, embed_dim, N) -> (B, N, embed_dim)
        x = self.projection(x)
        x = x.flatten(2).transpose(1, 2)
        x = self.norm(x)
        return x

class MultiHeadAttention(nn.Module):
    """改进的多头注意力机制"""
    
    def __init__(self, embed_dim, num_heads, dropout=0.1, qk_scale=None):
        super().__init__()
        assert embed_dim % num_heads == 0, "embed_dim必须能被num_heads整除"
        
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        self.scale = qk_scale or self.head_dim ** -0.5
        
        # 使用单个线性层生成Q, K, V
        self.qkv = nn.Linear(embed_dim, embed_dim * 3, bias=True)
        
        # 注意力dropout
        self.attention_dropout = nn.Dropout(dropout)
        
        # 输出投影
        self.projection = nn.Linear(embed_dim, embed_dim)
        self.projection_dropout = nn.Dropout(dropout)
        
        # 改进初始化
        self._init_weights()
        
    def _init_weights(self):
        """改进的权重初始化"""
        nn.init.xavier_uniform_(self.qkv.weight)
        nn.init.xavier_uniform_(self.projection.weight)
        nn.init.constant_(self.qkv.bias, 0)
        nn.init.constant_(self.projection.bias, 0)
        
    def forward(self, x):
        B, N, C = x.shape
        
        # 生成Q, K, V
        qkv = self.qkv(x).reshape(B, N, 3, self.num_heads, self.head_dim).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]  # 每个的形状: (B, num_heads, N, head_dim)
        
        # 计算注意力分数
        attention_scores = torch.matmul(q, k.transpose(-2, -1)) * self.scale
        attention_probs = torch.softmax(attention_scores, dim=-1)
        attention_probs = self.attention_dropout(attention_probs)
        
        # 应用注意力到值
        context = torch.matmul(attention_probs, v).transpose(1, 2).reshape(B, N, C)
        
        # 最终投影
        output = self.projection(context)
        output = self.projection_dropout(output)
        
        return output

class MLP(nn.Module):
    """改进的MLP模块"""
    
    def __init__(self, in_features, hidden_features=None, out_features=None, 
                 act_layer=nn.GELU, dropout=0.1):
        super().__init__()
        out_features = out_features or in_features
        hidden_features = hidden_features or in_features
        
        self.fc1 = nn.Linear(in_features, hidden_features)
        self.act = act_layer()
        self.dropout1 = nn.Dropout(dropout)
        self.fc2 = nn.Linear(hidden_features, out_features)
        self.dropout2 = nn.Dropout(dropout)
        
        # 改进初始化
        self._init_weights()
        
    def _init_weights(self):
        nn.init.xavier_uniform_(self.fc1.weight)
        nn.init.xavier_uniform_(self.fc2.weight)
        nn.init.constant_(self.fc1.bias, 0)
        nn.init.constant_(self.fc2.bias, 0)
        
    def forward(self, x):
        x = self.fc1(x)
        x = self.act(x)
        x = self.dropout1(x)
        x = self.fc2(x)
        x = self.dropout2(x)
        return x

class TransformerBlock(nn.Module):
    """改进的Transformer编码器块"""
    
    def __init__(self, embed_dim, num_heads, mlp_ratio=4.0, dropout=0.1, 
                 drop_path=0.0, act_layer=nn.GELU):
        super().__init__()
        
        # 预归一化结构
        self.norm1 = nn.LayerNorm(embed_dim)
        self.attention = MultiHeadAttention(
            embed_dim=embed_dim, 
            num_heads=num_heads, 
            dropout=dropout
        )
        
        # Stochastic Depth
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
        
        self.norm2 = nn.LayerNorm(embed_dim)
        mlp_hidden_dim = int(embed_dim * mlp_ratio)
        self.mlp = MLP(
            in_features=embed_dim,
            hidden_features=mlp_hidden_dim,
            act_layer=act_layer,
            dropout=dropout
        )
    
    def forward(self, x):
        # 预归一化 + 残差连接
        x = x + self.drop_path(self.attention(self.norm1(x)))
        x = x + self.drop_path(self.mlp(self.norm2(x)))
        return x

class DropPath(nn.Module):
    """Stochastic Depth实现"""
    
    def __init__(self, drop_prob=None):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob

    def forward(self, x):
        if self.drop_prob == 0. or not self.training:
            return x
        keep_prob = 1 - self.drop_prob
        shape = (x.shape[0],) + (1,) * (x.ndim - 1)
        random_tensor = keep_prob + torch.rand(shape, dtype=x.dtype, device=x.device)
        random_tensor.floor_()
        output = x.div(keep_prob) * random_tensor
        return output

class ImprovedVisionTransformer(nn.Module):
    """改进的Vision Transformer模型"""
    
    def __init__(self, image_size=224, patch_size=16, in_channels=3, 
                 embed_dim=384, depth=12, num_heads=12, mlp_ratio=4.0,
                 output_dim=128, dropout=0.1, drop_path_rate=0.1):
        super().__init__()
        
        self.num_patches = (image_size // patch_size) ** 2
        self.embed_dim = embed_dim
        self.depth = depth
        
        # Patch嵌入
        self.patch_embed = PatchEmbedding(
            image_size=image_size,
            patch_size=patch_size, 
            in_channels=in_channels,
            embed_dim=embed_dim
        )
        
        # 分类token
        self.cls_token = nn.Parameter(torch.zeros(1, 1, embed_dim))
        
        # 位置编码 - 使用可学习的位置编码
        self.pos_embed = nn.Parameter(torch.zeros(1, self.num_patches + 1, embed_dim))
        self.pos_dropout = nn.Dropout(dropout)
        
        # Stochastic depth衰减规则
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, depth)]
        
        # Transformer编码器层
        self.blocks = nn.ModuleList([
            TransformerBlock(
                embed_dim=embed_dim,
                num_heads=num_heads,
                mlp_ratio=mlp_ratio,
                dropout=dropout,
                drop_path=dpr[i]
            ) for i in range(depth)
        ])
        
        # 最终归一化
        self.norm = nn.LayerNorm(embed_dim)
        
        # 分类头 - 增强版本
        self.pre_logits = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.Tanh()
        )
        
        # 特征头
        self.feature_head = nn.Sequential(
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim // 2),
            nn.LayerNorm(embed_dim // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim // 2, output_dim),
            nn.ReLU()
        )
        
        # 回归头
        self.regression_head = nn.Sequential(
            nn.Dropout(dropout),
            nn.Linear(output_dim, 128),
            nn.LayerNorm(128),
            nn.GELU(),
            nn.Dropout(dropout / 2),
            nn.Linear(128, 64),
            nn.GELU(),
            nn.Dropout(dropout / 2),
            nn.Linear(64, 1)
        )
        
        # 权重初始化
        self._initialize_weights()
        
    def _initialize_weights(self):
        """改进的权重初始化"""
        # 位置编码初始化
        nn.init.trunc_normal_(self.pos_embed, std=0.02)
        nn.init.trunc_normal_(self.cls_token, std=0.02)
        
        # 应用初始化到所有线性层
        self.apply(self._init_weights)
        
    def _init_weights(self, m):
        """权重初始化函数"""
        if isinstance(m, nn.Linear):
            nn.init.trunc_normal_(m.weight, std=0.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.Conv2d):
            fan_out = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
            fan_out //= m.groups
            m.weight.data.normal_(0, math.sqrt(2.0 / fan_out))
            if m.bias is not None:
                m.bias.data.zero_()
    
    def interpolate_pos_encoding(self, x, w, h):
        """位置编码插值（用于不同尺寸的输入）"""
        npatch = x.shape[1] - 1
        N = self.pos_embed.shape[1] - 1
        if npatch == N and w == h:
            return self.pos_embed
        
        class_pos_embed = self.pos_embed[:, 0]
        patch_pos_embed = self.pos_embed[:, 1:]
        dim = x.shape[-1]
        
        w0 = w // self.patch_embed.patch_size
        h0 = h // self.patch_embed.patch_size
        
        patch_pos_embed = nn.functional.interpolate(
            patch_pos_embed.reshape(1, int(math.sqrt(N)), int(math.sqrt(N)), dim).permute(0, 3, 1, 2),
            size=(w0, h0),
            mode='bicubic',
        )
        
        patch_pos_embed = patch_pos_embed.permute(0, 2, 3, 1).view(1, -1, dim)
        return torch.cat((class_pos_embed.unsqueeze(0), patch_pos_embed), dim=1)
    
    def forward(self, x):
        B, C, H, W = x.shape
        
        # Patch嵌入
        x = self.patch_embed(x)  # (B, num_patches, embed_dim)
        
        # 添加分类token
        cls_tokens = self.cls_token.expand(B, -1, -1)  # (B, 1, embed_dim)
        x = torch.cat((cls_tokens, x), dim=1)  # (B, num_patches + 1, embed_dim)
        
        # 添加位置编码
        x = x + self.interpolate_pos_encoding(x, W, H)
        x = self.pos_dropout(x)
        
        # Transformer编码器
        for block in self.blocks:
            x = block(x)
        
        x = self.norm(x)
        
        # 提取分类token
        cls_output = x[:, 0]
        
        # 预logits
        cls_output = self.pre_logits(cls_output)
        
        # 生成特征和预测
        features = self.feature_head(cls_output)
        output = self.regression_head(features)
        
        return output.squeeze(-1), features

class ImprovedViTTrainer:
    """改进的ViT训练器"""
    
    def __init__(self, model, device='cuda', learning_rate=0.001, weight_decay=0.05):
        self.model = model.to(device)
        self.device = device
        self.criterion = nn.SmoothL1Loss()
        
        # 使用AdamW优化器（ViT的标准选择）
        self.optimizer = optim.AdamW(
            model.parameters(), 
            lr=learning_rate, 
            weight_decay=weight_decay,
            betas=(0.9, 0.999),
            eps=1e-8
        )
        
        # 使用余弦退火学习率调度，包含warmup
        self.warmup_epochs = 10
        self.total_epochs = 50
        
        self.train_losses = []
        self.val_losses = []
        self.train_r2_scores = []
        self.val_r2_scores = []
        self.learning_rates = []
        
        self.best_val_loss = float('inf')
        self.patience_counter = 0
        self.max_patience = 20
    
    def get_lr(self, epoch):
        """获取当前epoch的学习率（包含warmup）"""
        if epoch < self.warmup_epochs:
            # Warmup阶段：线性增长
            return self.optimizer.param_groups[0]['lr'] * epoch / self.warmup_epochs
        else:
            # 余弦退火
            progress = (epoch - self.warmup_epochs) / (self.total_epochs - self.warmup_epochs)
            return self.optimizer.param_groups[0]['lr'] * 0.5 * (1 + math.cos(math.pi * progress))
    
    def train_epoch(self, train_loader, epoch):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        predictions = []
        targets = []
        batch_count = 0
        
        # 设置当前epoch的学习率
        current_lr = self.get_lr(epoch)
        for param_group in self.optimizer.param_groups:
            param_group['lr'] = current_lr
        self.learning_rates.append(current_lr)
        
        for batch_idx, batch in enumerate(train_loader):
            images = batch['image'].to(self.device)
            energy = batch['energy'].to(self.device)
            
            self.optimizer.zero_grad()
            outputs, _ = self.model(images)
            loss = self.criterion(outputs, energy)
            
            # 检查loss有效性
            if torch.isnan(loss) or torch.isinf(loss):
                logger.warning(f"检测到异常loss: {loss.item()}, 跳过该批次")
                continue
            
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            total_loss += loss.item()
            batch_count += 1
            
            # 收集预测结果
            pred_np = outputs.detach().cpu().numpy()
            target_np = energy.detach().cpu().numpy()
            
            if pred_np.ndim == 0:
                pred_np = [pred_np.item()]
            if target_np.ndim == 0:
                target_np = [target_np.item()]
            
            predictions.extend(pred_np.tolist())
            targets.extend(target_np.tolist())
            
            if batch_idx % 10 == 0:
                logger.info(f'Epoch {epoch+1}, 批次 {batch_idx}/{len(train_loader)}, '
                          f'损失: {loss.item():.4f}, 学习率: {current_lr:.6f}')
        
        if batch_count == 0:
            return 0, 0
        
        avg_loss = total_loss / batch_count
        
        # 计算R²
        try:
            if len(set(targets)) > 1 and len(predictions) > 1:
                r2 = r2_score(targets, predictions)
            else:
                r2 = 0.0
        except Exception as e:
            logger.warning(f"R²计算失败: {e}")
            r2 = 0.0
        
        return avg_loss, r2
    
    def validate(self, val_loader):
        """验证模型"""
        self.model.eval()
        total_loss = 0
        predictions = []
        targets = []
        batch_count = 0
        
        with torch.no_grad():
            for batch in val_loader:
                images = batch['image'].to(self.device)
                energy = batch['energy'].to(self.device)
                
                outputs, _ = self.model(images)
                loss = self.criterion(outputs, energy)
                
                if not (torch.isnan(loss) or torch.isinf(loss)):
                    total_loss += loss.item()
                    batch_count += 1
                    
                    pred_np = outputs.cpu().numpy()
                    target_np = energy.cpu().numpy()
                    
                    if pred_np.ndim == 0:
                        pred_np = [pred_np.item()]
                    if target_np.ndim == 0:
                        target_np = [target_np.item()]
                    
                    predictions.extend(pred_np.tolist())
                    targets.extend(target_np.tolist())
        
        if batch_count == 0:
            return float('inf'), 0, float('inf'), float('inf'), [], []
        
        avg_loss = total_loss / batch_count
        
        # 计算指标
        try:
            if len(set(targets)) > 1 and len(predictions) > 1:
                r2 = r2_score(targets, predictions)
                rmse = np.sqrt(mean_squared_error(targets, predictions))
                mae = mean_absolute_error(targets, predictions)
            else:
                r2 = 0.0
                rmse = float('inf')
                mae = float('inf')
        except Exception as e:
            logger.warning(f"指标计算失败: {e}")
            r2 = 0.0
            rmse = float('inf')
            mae = float('inf')
        
        return avg_loss, r2, rmse, mae, predictions, targets
    
    def train(self, train_loader, val_loader, epochs=50, save_dir='./results/saved_models'):
        """训练模型"""
        os.makedirs(save_dir, exist_ok=True)
        best_model_path = None
        self.total_epochs = epochs
        
        logger.info(f"开始训练ViT模型，总共 {epochs} 个epoch")
        logger.info(f"Warmup期: {self.warmup_epochs} epochs")
        
        for epoch in range(epochs):
            # 训练
            train_loss, train_r2 = self.train_epoch(train_loader, epoch)
            
            # 验证
            val_loss, val_r2, val_rmse, val_mae, _, _ = self.validate(val_loader)
            
            # 记录历史
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.train_r2_scores.append(train_r2)
            self.val_r2_scores.append(val_r2)
            
            current_lr = self.learning_rates[-1] if self.learning_rates else 0
            
            logger.info(f'Epoch {epoch+1}/{epochs}:')
            logger.info(f'  学习率: {current_lr:.6f}')
            logger.info(f'  训练损失: {train_loss:.4f}, 训练R²: {train_r2:.4f}')
            logger.info(f'  验证损失: {val_loss:.4f}, 验证R²: {val_r2:.4f}')
            logger.info(f'  验证RMSE: {val_rmse:.4f}, 验证MAE: {val_mae:.4f}')
            
            # 保存最佳模型
            if val_loss < self.best_val_loss and val_loss != float('inf'):
                self.best_val_loss = val_loss
                self.patience_counter = 0
                best_model_path = os.path.join(save_dir, 'shenyang_ViT_improved.pt')
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'val_loss': val_loss,
                    'val_r2': val_r2,
                    'config': {
                        'model_type': 'ViT-Improved',
                        'image_size': 224,
                        'patch_size': 16,
                        'embed_dim': 384,
                        'depth': 12,
                        'num_heads': 12,
                        'output_dim': 128,
                        'dropout': 0.1
                    }
                }, best_model_path)
                logger.info(f'保存最佳模型: {best_model_path}')
            else:
                self.patience_counter += 1
                if self.patience_counter >= self.max_patience:
                    logger.info(f'早停: 验证损失在 {self.max_patience} 个epoch内未改善')
                    break
        
        logger.info(f"训练完成! 最佳验证损失: {self.best_val_loss:.4f}")
        return best_model_path
    
    def plot_training_history(self, save_path='vit_improved_training_history.png'):
        """绘制训练历史"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # 损失曲线
        ax1.plot(self.train_losses, label='训练损失', color='blue', linewidth=2)
        ax1.plot(self.val_losses, label='验证损失', color='red', linewidth=2)
        ax1.set_title('ViT (改进版) 训练和验证损失', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('SmoothL1 Loss')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # R²分数
        ax2.plot(self.train_r2_scores, label='训练R²', color='blue', linewidth=2)
        ax2.plot(self.val_r2_scores, label='验证R²', color='red', linewidth=2)
        ax2.set_title('ViT R²分数变化', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('R² Score')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 学习率变化
        ax3.plot(self.learning_rates, color='green', linewidth=2)
        ax3.set_title('学习率变化 (Warmup + 余弦退火)', fontsize=14, fontweight='bold')
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('Learning Rate')
        ax3.grid(True, alpha=0.3)
        
        # 改进信息
        info_text = f"""ViT 改进版信息:
        
• 模型规模: embed_dim=384, depth=12, heads=12
• 损失函数: SmoothL1Loss
• 优化器: AdamW (ViT专用)
• 学习率调度: Warmup + 余弦退火
• Warmup轮数: {self.warmup_epochs}
• Stochastic Depth: 0.1
• 权重初始化: trunc_normal_(std=0.02)
• 位置编码: 可学习 + 插值支持
• 总训练轮数: {len(self.train_losses)}
• 最佳验证R²: {max(self.val_r2_scores) if self.val_r2_scores else 0:.4f}
• 最低验证损失: {min(self.val_losses) if self.val_losses else float('inf'):.4f}

主要改进:
• 🔄 Warmup + 余弦退火学习率
• 📐 可学习位置编码 + 插值
• 🎯 Stochastic Depth正则化
• 💪 改进的权重初始化
• 🔧 预归一化Transformer结构
        """
        ax4.text(0.05, 0.95, info_text, transform=ax4.transAxes, 
                verticalalignment='top', fontsize=9,
                bbox=dict(boxstyle='round', facecolor='lightcyan', alpha=0.8))
        ax4.set_title('模型改进信息', fontsize=14, fontweight='bold')
        ax4.axis('off')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        logger.info(f"ViT改进版训练历史图保存至: {save_path}")

def test_model(model_path, test_loader, device='cuda'):
    """测试改进的ViT模型"""
    checkpoint = torch.load(model_path, map_location=device)
    config = checkpoint['config']
    
    model = ImprovedVisionTransformer(
        image_size=config['image_size'],
        patch_size=config['patch_size'],
        embed_dim=config['embed_dim'],
        depth=config['depth'],
        num_heads=config['num_heads'],
        output_dim=config['output_dim'],
        dropout=0.0  # 测试时不使用dropout
    ).to(device)
    
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    predictions = []
    targets = []
    region_ids = []
    
    with torch.no_grad():
        for batch in test_loader:
            images = batch['image'].to(device)
            energy = batch['energy'].to(device)
            batch_region_ids = batch['region_id']
            
            outputs, _ = model(images)
            
            predictions.extend(outputs.cpu().numpy())
            targets.extend(energy.cpu().numpy())
            
            if torch.is_tensor(batch_region_ids):
                region_ids.extend(batch_region_ids.cpu().numpy())
            else:
                region_ids.extend(batch_region_ids)
    
    # 计算指标
    r2 = r2_score(targets, predictions)
    rmse = np.sqrt(mean_squared_error(targets, predictions))
    mae = mean_absolute_error(targets, predictions)
    
    # 安全计算MAPE
    targets_array = np.array(targets)
    predictions_array = np.array(predictions)
    non_zero_mask = targets_array != 0
    if np.any(non_zero_mask):
        mape = np.mean(np.abs((targets_array[non_zero_mask] - predictions_array[non_zero_mask]) / targets_array[non_zero_mask])) * 100
    else:
        mape = float('inf')
    
    results = {
        'model_type': 'ViT-Improved',
        'r2_score': r2,
        'rmse': rmse,
        'mae': mae,
        'mape': mape,
        'predictions': predictions,
        'targets': targets,
        'region_ids': region_ids
    }
    
    logger.info("=== ViT改进版模型测试结果 ===")
    logger.info(f"R² Score: {r2:.4f}")
    logger.info(f"RMSE: {rmse:.4f}")
    logger.info(f"MAE: {mae:.4f}")
    logger.info(f"MAPE: {mape:.2f}%")
    
    return results

def main():
    """主函数"""
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    from data_loader import create_data_loaders, CONFIGS
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        logger.info(f"GPU显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    # 加载数据
    config = CONFIGS['shenyang']
    train_loader, val_loader, test_loader = create_data_loaders(
        config, 
        batch_size=6,  # ViT需要稍小的批次
        num_workers=0,
        max_images_per_region=3
    )
    
    # 创建改进的ViT模型
    model = ImprovedVisionTransformer(
        image_size=224,
        patch_size=16,
        embed_dim=384,  # 增加嵌入维度
        depth=12,       # 增加深度
        num_heads=12,   # 增加注意力头数
        output_dim=128,
        dropout=0.1,
        drop_path_rate=0.1
    )
    
    # 打印模型信息
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.info(f"模型参数总数: {total_params:,}")
    logger.info(f"可训练参数: {trainable_params:,}")
    logger.info(f"模型大小: {total_params * 4 / 1024 / 1024:.1f} MB")
    
    # 创建训练器
    trainer = ImprovedViTTrainer(
        model, 
        device=device, 
        learning_rate=0.001,  # ViT适合较大的学习率
        weight_decay=0.05
    )
    
    # 训练模型
    best_model_path = trainer.train(
        train_loader, 
        val_loader, 
        epochs=50,
        save_dir='./results/saved_models'
    )
    
    # 绘制训练历史
    trainer.plot_training_history()
    
    # 测试模型
    if best_model_path:
        test_results = test_model(best_model_path, test_loader, device)
        
        # 保存结果
        os.makedirs('./results', exist_ok=True)
        results_path = './results/vit_improved_test_results.json'
        with open(results_path, 'w') as f:
            json_results = {
                'model_type': test_results['model_type'],
                'r2_score': test_results['r2_score'],
                'rmse': test_results['rmse'],
                'mae': test_results['mae'],
                'mape': test_results['mape'],
                'predictions': [float(x) for x in test_results['predictions']],
                'targets': [float(x) for x in test_results['targets']],
                'region_ids': [int(x) for x in test_results['region_ids']]
            }
            json.dump(json_results, f, indent=2)
        
        logger.info(f"测试结果保存至: {results_path}")

if __name__ == "__main__":
    main()