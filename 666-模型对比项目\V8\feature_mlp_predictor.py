"""
基于特征的MLP预测器 - 两阶段方法的第二阶段
使用预提取的特征进行能耗预测
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pickle
import os
import json
import logging
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FeatureMLP(nn.Module):
    """基于特征的MLP回归器"""
    
    def __init__(self, input_dim=1024, hidden_dims=[512, 256, 128], dropout=0.3):
        """
        Args:
            input_dim: 输入特征维度
            hidden_dims: 隐藏层维度列表
            dropout: Dropout概率
        """
        super(FeatureMLP, self).__init__()
        
        layers = []
        prev_dim = input_dim
        
        # 构建隐藏层
        for hidden_dim in hidden_dims:
            layers.append(nn.Linear(prev_dim, hidden_dim))
            layers.append(nn.BatchNorm1d(hidden_dim))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout))
            prev_dim = hidden_dim
        
        # 输出层
        layers.append(nn.Linear(prev_dim, 1))
        
        self.model = nn.Sequential(*layers)
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化模型权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        """前向传播"""
        return self.model(x).squeeze(-1)

class FeatureMLPTrainer:
    """MLP训练器"""
    
    def __init__(self, model, device='cuda', learning_rate=0.001, weight_decay=1e-4):
        self.model = model.to(device)
        self.device = device
        self.criterion = nn.MSELoss()
        self.optimizer = optim.Adam(
            model.parameters(), 
            lr=learning_rate, 
            weight_decay=weight_decay
        )
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, 
            mode='min', 
            factor=0.5, 
            patience=15, 
            verbose=True
        )
        
        # 训练历史
        self.train_losses = []
        self.val_losses = []
        self.train_r2_scores = []
        self.val_r2_scores = []
        
        # 特征标准化器
        self.feature_scaler = StandardScaler()
        self.label_scaler = StandardScaler()
    
    def prepare_data(self, features, labels, fit_scalers=True):
        """准备和标准化数据"""
        if fit_scalers:
            # 拟合标准化器
            features_scaled = self.feature_scaler.fit_transform(features)
            labels_scaled = self.label_scaler.fit_transform(labels.reshape(-1, 1)).flatten()
        else:
            # 使用已拟合的标准化器
            features_scaled = self.feature_scaler.transform(features)
            labels_scaled = self.label_scaler.transform(labels.reshape(-1, 1)).flatten()
        
        return features_scaled, labels_scaled
    
    def train_epoch(self, train_features, train_labels):
        """训练一个epoch"""
        self.model.train()
        
        # 转换为tensor
        features_tensor = torch.FloatTensor(train_features).to(self.device)
        labels_tensor = torch.FloatTensor(train_labels).to(self.device)
        
        self.optimizer.zero_grad()
        outputs = self.model(features_tensor)
        loss = self.criterion(outputs, labels_tensor)
        loss.backward()
        self.optimizer.step()
        
        # 计算R²分数
        with torch.no_grad():
            predictions = outputs.cpu().numpy()
            targets = labels_tensor.cpu().numpy()
            r2 = r2_score(targets, predictions)
        
        return loss.item(), r2
    
    def validate(self, val_features, val_labels):
        """验证模型"""
        self.model.eval()
        
        with torch.no_grad():
            features_tensor = torch.FloatTensor(val_features).to(self.device)
            labels_tensor = torch.FloatTensor(val_labels).to(self.device)
            
            outputs = self.model(features_tensor)
            loss = self.criterion(outputs, labels_tensor)
            
            predictions = outputs.cpu().numpy()
            targets = labels_tensor.cpu().numpy()
            
            r2 = r2_score(targets, predictions)
            rmse = np.sqrt(mean_squared_error(targets, predictions))
            mae = mean_absolute_error(targets, predictions)
        
        return loss.item(), r2, rmse, mae, predictions, targets
    
    def train(self, train_features, train_labels, val_features, val_labels, 
              epochs=200, save_dir='./saved_models', model_name='feature_mlp'):
        """训练模型"""
        os.makedirs(save_dir, exist_ok=True)
        
        # 数据标准化
        train_features_scaled, train_labels_scaled = self.prepare_data(
            train_features, train_labels, fit_scalers=True
        )
        val_features_scaled, val_labels_scaled = self.prepare_data(
            val_features, val_labels, fit_scalers=False
        )
        
        best_val_loss = float('inf')
        best_model_path = None
        patience_counter = 0
        
        logger.info(f"开始训练 {model_name} 模型，总共 {epochs} 个epoch")
        
        for epoch in range(epochs):
            # 训练
            train_loss, train_r2 = self.train_epoch(train_features_scaled, train_labels_scaled)
            
            # 验证
            val_loss, val_r2, val_rmse, val_mae, _, _ = self.validate(
                val_features_scaled, val_labels_scaled
            )
            
            # 学习率调度
            self.scheduler.step(val_loss)
            
            # 记录历史
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.train_r2_scores.append(train_r2)
            self.val_r2_scores.append(val_r2)
            
            # 打印进度
            if epoch % 20 == 0 or epoch == epochs - 1:
                logger.info(f'Epoch {epoch+1}/{epochs}:')
                logger.info(f'  训练损失: {train_loss:.4f}, 训练R²: {train_r2:.4f}')
                logger.info(f'  验证损失: {val_loss:.4f}, 验证R²: {val_r2:.4f}')
                logger.info(f'  验证RMSE: {val_rmse:.4f}, 验证MAE: {val_mae:.4f}')
            
            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                best_model_path = os.path.join(save_dir, f'{model_name}_best.pt')
                
                # 保存模型和标准化器
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'val_loss': val_loss,
                    'val_r2': val_r2,
                    'feature_scaler': self.feature_scaler,
                    'label_scaler': self.label_scaler,
                    'config': {
                        'input_dim': self.model.model[0].in_features,
                        'model_name': model_name
                    }
                }, best_model_path)
                
                if epoch % 20 == 0:
                    logger.info(f'保存最佳模型: {best_model_path}')
            else:
                patience_counter += 1
            
            # 早停
            if patience_counter >= 30:
                logger.info(f"早停触发，在epoch {epoch+1}")
                break
        
        logger.info(f"训练完成! 最佳验证损失: {best_val_loss:.4f}")
        return best_model_path
    
    def plot_training_history(self, save_path='feature_mlp_training_history.png'):
        """绘制训练历史"""
        fig, ((ax1, ax2)) = plt.subplots(1, 2, figsize=(12, 5))
        
        # 损失曲线
        ax1.plot(self.train_losses, label='训练损失', color='blue')
        ax1.plot(self.val_losses, label='验证损失', color='red')
        ax1.set_title('训练和验证损失')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('MSE Loss')
        ax1.legend()
        ax1.grid(True)
        
        # R²分数
        ax2.plot(self.train_r2_scores, label='训练R²', color='blue')
        ax2.plot(self.val_r2_scores, label='验证R²', color='red')
        ax2.set_title('训练和验证R²分数')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('R² Score')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        logger.info(f"训练历史图保存至: {save_path}")

def load_features(feature_path):
    """加载特征数据"""
    with open(feature_path, 'rb') as f:
        data = pickle.load(f)
    return data

def test_feature_mlp(model_path, test_features, test_labels, test_region_ids, model_name):
    """测试特征MLP模型"""
    # 加载模型
    checkpoint = torch.load(model_path, map_location='cuda')
    
    # 重建模型
    input_dim = checkpoint['config']['input_dim']
    model = FeatureMLP(input_dim=input_dim)
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.cuda()  # 确保模型在GPU上
    model.eval()
    
    # 加载标准化器
    feature_scaler = checkpoint['feature_scaler']
    label_scaler = checkpoint['label_scaler']
    
    # 标准化测试特征
    test_features_scaled = feature_scaler.transform(test_features)
    
    # 预测
    with torch.no_grad():
        features_tensor = torch.FloatTensor(test_features_scaled).cuda()
        outputs = model(features_tensor)
        predictions_scaled = outputs.cpu().numpy()
    
    # 反标准化预测结果
    predictions = label_scaler.inverse_transform(predictions_scaled.reshape(-1, 1)).flatten()
    
    # 计算指标
    r2 = r2_score(test_labels, predictions)
    rmse = np.sqrt(mean_squared_error(test_labels, predictions))
    mae = mean_absolute_error(test_labels, predictions)
    mape = np.mean(np.abs((test_labels - predictions) / test_labels)) * 100
    
    results = {
        'model_type': f'{model_name}_FeatureMLP',
        'r2_score': r2,
        'rmse': rmse,
        'mae': mae,
        'mape': mape,
        'predictions': predictions.tolist(),
        'targets': test_labels.tolist(),
        'region_ids': test_region_ids.tolist()
    }
    
    logger.info(f"=== {model_name} 特征MLP测试结果 ===")
    logger.info(f"R² Score: {r2:.4f}")
    logger.info(f"RMSE: {rmse:.4f}")
    logger.info(f"MAE: {mae:.4f}")
    logger.info(f"MAPE: {mape:.2f}%")
    
    return results

def train_and_test_feature_mlp(model_name='mobilenet', feature_dim=1024):
    """训练和测试特征MLP"""
    logger.info(f"\n{'='*60}")
    logger.info(f"训练和测试 {model_name} 特征MLP")
    logger.info(f"{'='*60}")
    
    # 加载特征数据
    train_data = load_features(f'features/features_{model_name}_train.pkl')
    val_data = load_features(f'features/features_{model_name}_val.pkl')
    test_data = load_features(f'features/features_{model_name}_test.pkl')
    
    # 创建模型
    model = FeatureMLP(input_dim=feature_dim, hidden_dims=[512, 256, 128], dropout=0.3)
    
    # 创建训练器
    trainer = FeatureMLPTrainer(model, device='cuda', learning_rate=0.001)
    
    # 训练模型
    best_model_path = trainer.train(
        train_data['features'], train_data['labels'],
        val_data['features'], val_data['labels'],
        epochs=200,
        model_name=f'{model_name}_feature_mlp'
    )
    
    # 绘制训练历史
    trainer.plot_training_history(f'{model_name}_feature_mlp_training_history.png')
    
    # 测试模型
    test_results = test_feature_mlp(
        best_model_path, 
        test_data['features'], 
        test_data['labels'], 
        test_data['region_ids'],
        model_name
    )
    
    # 保存结果
    results_path = f'{model_name}_feature_mlp_test_results.json'
    with open(results_path, 'w') as f:
        json.dump(test_results, f, indent=2)
    
    logger.info(f"测试结果保存至: {results_path}")
    return test_results

if __name__ == "__main__":
    # 测试特征MLP
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 训练和测试不同模型的特征MLP
    model_names = ['mobilenet', 'densenet', 'resnet']
    
    for model_name in model_names:
        try:
            train_and_test_feature_mlp(model_name, feature_dim=1024)
        except Exception as e:
            logger.error(f"{model_name} 特征MLP训练失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
