"""
多层感知器(MLP)模型
用于基于街景图像特征预测街区能耗
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import os
import json
from datetime import datetime
import logging
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import matplotlib.pyplot as plt

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MLPModel(nn.Module):
    """多层感知器模型"""
    
    def __init__(self, input_dim=128, hidden_dims=[256, 128, 64], output_dim=1, dropout=0.3):
        """
        Args:
            input_dim: 输入特征维度
            hidden_dims: 隐藏层维度列表
            output_dim: 输出维度（能耗预测为1）
            dropout: Dropout概率
        """
        super(MLPModel, self).__init__()
        
        layers = []
        prev_dim = input_dim
        
        # 构建隐藏层
        for hidden_dim in hidden_dims:
            layers.append(nn.Linear(prev_dim, hidden_dim))
            layers.append(nn.BatchNorm1d(hidden_dim))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout))
            prev_dim = hidden_dim
        
        # 输出层
        layers.append(nn.Linear(prev_dim, output_dim))
        
        self.model = nn.Sequential(*layers)
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化模型权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        """前向传播"""
        return self.model(x)

class ImageFeatureExtractor(nn.Module):
    """简单的图像特征提取器"""
    
    def __init__(self, output_dim=128):
        super(ImageFeatureExtractor, self).__init__()
        
        self.conv_layers = nn.Sequential(
            # 第一个卷积块
            nn.Conv2d(3, 32, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(),
            nn.MaxPool2d(2, 2),
            
            # 第二个卷积块
            nn.Conv2d(32, 64, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(),
            nn.MaxPool2d(2, 2),
            
            # 第三个卷积块
            nn.Conv2d(64, 128, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(),
            nn.MaxPool2d(2, 2),
            
            # 第四个卷积块
            nn.Conv2d(128, 256, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(),
            nn.AdaptiveAvgPool2d((4, 4))
        )
        
        self.fc_layers = nn.Sequential(
            nn.Linear(256 * 4 * 4, 512),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(512, output_dim)
        )
    
    def forward(self, x):
        x = self.conv_layers(x)
        x = x.view(x.size(0), -1)
        x = self.fc_layers(x)
        return x

class StreetViewMLPPredictor(nn.Module):
    """街景图像MLP预测器完整模型"""
    
    def __init__(self, feature_dim=128, mlp_hidden_dims=[256, 128, 64], dropout=0.3):
        super(StreetViewMLPPredictor, self).__init__()
        
        self.feature_extractor = ImageFeatureExtractor(output_dim=feature_dim)
        self.mlp = MLPModel(
            input_dim=feature_dim,
            hidden_dims=mlp_hidden_dims,
            output_dim=1,
            dropout=dropout
        )
    
    def forward(self, x):
        features = self.feature_extractor(x)
        output = self.mlp(features)
        return output.squeeze()

class MLPTrainer:
    """MLP模型训练器"""
    
    def __init__(self, model, device='cuda', learning_rate=0.001, weight_decay=1e-4):
        self.model = model.to(device)
        self.device = device
        self.criterion = nn.MSELoss()
        self.optimizer = optim.Adam(
            model.parameters(), 
            lr=learning_rate, 
            weight_decay=weight_decay
        )
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, 
            mode='min', 
            factor=0.5, 
            patience=10, 
            verbose=True
        )
        
        # 训练历史
        self.train_losses = []
        self.val_losses = []
        self.train_r2_scores = []
        self.val_r2_scores = []
    
    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        predictions = []
        targets = []
        
        for batch_idx, batch in enumerate(train_loader):
            images = batch['image'].to(self.device)
            energy = batch['energy'].to(self.device)
            
            self.optimizer.zero_grad()
            outputs = self.model(images)
            loss = self.criterion(outputs, energy)
            loss.backward()
            self.optimizer.step()
            
            total_loss += loss.item()
            predictions.extend(outputs.detach().cpu().numpy())
            targets.extend(energy.detach().cpu().numpy())
            
            if batch_idx % 10 == 0:
                logger.info(f'训练批次 {batch_idx}/{len(train_loader)}, 损失: {loss.item():.4f}')
        
        avg_loss = total_loss / len(train_loader)
        r2 = r2_score(targets, predictions)
        
        return avg_loss, r2
    
    def validate(self, val_loader):
        """验证模型"""
        self.model.eval()
        total_loss = 0
        predictions = []
        targets = []
        
        with torch.no_grad():
            for batch in val_loader:
                images = batch['image'].to(self.device)
                energy = batch['energy'].to(self.device)
                
                outputs = self.model(images)
                loss = self.criterion(outputs, energy)
                
                total_loss += loss.item()
                predictions.extend(outputs.cpu().numpy())
                targets.extend(energy.cpu().numpy())
        
        avg_loss = total_loss / len(val_loader)
        r2 = r2_score(targets, predictions)
        rmse = np.sqrt(mean_squared_error(targets, predictions))
        mae = mean_absolute_error(targets, predictions)
        
        return avg_loss, r2, rmse, mae, predictions, targets
    
    def train(self, train_loader, val_loader, epochs=100, save_dir='./saved_models'):
        """训练模型"""
        os.makedirs(save_dir, exist_ok=True)
        best_val_loss = float('inf')
        best_model_path = None
        
        logger.info(f"开始训练MLP模型，总共 {epochs} 个epoch")
        
        for epoch in range(epochs):
            # 训练
            train_loss, train_r2 = self.train_epoch(train_loader)
            
            # 验证
            val_loss, val_r2, val_rmse, val_mae, _, _ = self.validate(val_loader)
            
            # 学习率调度
            self.scheduler.step(val_loss)
            
            # 记录历史
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.train_r2_scores.append(train_r2)
            self.val_r2_scores.append(val_r2)
            
            logger.info(f'Epoch {epoch+1}/{epochs}:')
            logger.info(f'  训练损失: {train_loss:.4f}, 训练R²: {train_r2:.4f}')
            logger.info(f'  验证损失: {val_loss:.4f}, 验证R²: {val_r2:.4f}')
            logger.info(f'  验证RMSE: {val_rmse:.4f}, 验证MAE: {val_mae:.4f}')
            
            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                best_model_path = os.path.join(save_dir, 'shenyang_MLP_best.pt')
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'val_loss': val_loss,
                    'val_r2': val_r2,
                    'config': {
                        'model_type': 'MLP',
                        'feature_dim': 128,
                        'mlp_hidden_dims': [256, 128, 64],
                        'dropout': 0.3
                    }
                }, best_model_path)
                logger.info(f'保存最佳模型: {best_model_path}')
        
        logger.info(f"训练完成! 最佳验证损失: {best_val_loss:.4f}")
        return best_model_path
    
    def plot_training_history(self, save_path='mlp_training_history.png'):
        """绘制训练历史"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))
        
        # 损失曲线
        ax1.plot(self.train_losses, label='训练损失', color='blue')
        ax1.plot(self.val_losses, label='验证损失', color='red')
        ax1.set_title('训练和验证损失')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('MSE Loss')
        ax1.legend()
        ax1.grid(True)
        
        # R²分数
        ax2.plot(self.train_r2_scores, label='训练R²', color='blue')
        ax2.plot(self.val_r2_scores, label='验证R²', color='red')
        ax2.set_title('训练和验证R²分数')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('R² Score')
        ax2.legend()
        ax2.grid(True)
        
        # 学习率
        ax3.plot([self.optimizer.param_groups[0]['lr']] * len(self.train_losses))
        ax3.set_title('学习率变化')
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('Learning Rate')
        ax3.grid(True)
        
        # 最后的预测散点图（如果有验证数据）
        ax4.text(0.5, 0.5, 'MLP模型训练历史\n请运行测试以查看预测结果', 
                ha='center', va='center', transform=ax4.transAxes)
        ax4.set_title('模型信息')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        logger.info(f"训练历史图保存至: {save_path}")

def test_model(model_path, test_loader, device='cuda'):
    """测试模型"""
    # 加载模型
    checkpoint = torch.load(model_path, map_location=device)
    config = checkpoint['config']
    
    model = StreetViewMLPPredictor(
        feature_dim=config['feature_dim'],
        mlp_hidden_dims=config['mlp_hidden_dims'],
        dropout=0.0  # 测试时不使用dropout
    ).to(device)
    
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    predictions = []
    targets = []
    region_ids = []
    
    with torch.no_grad():
        for batch in test_loader:
            images = batch['image'].to(device)
            energy = batch['energy'].to(device)
            batch_region_ids = batch['region_id']
            
            outputs = model(images)
            
            predictions.extend(outputs.cpu().numpy())
            targets.extend(energy.cpu().numpy())
            region_ids.extend(batch_region_ids.numpy() if torch.is_tensor(batch_region_ids) else batch_region_ids)
    
    # 计算指标
    r2 = r2_score(targets, predictions)
    rmse = np.sqrt(mean_squared_error(targets, predictions))
    mae = mean_absolute_error(targets, predictions)
    mape = np.mean(np.abs((np.array(targets) - np.array(predictions)) / np.array(targets))) * 100
    
    results = {
        'model_type': 'MLP',
        'r2_score': r2,
        'rmse': rmse,
        'mae': mae,
        'mape': mape,
        'predictions': predictions,
        'targets': targets,
        'region_ids': region_ids
    }
    
    logger.info("=== MLP模型测试结果 ===")
    logger.info(f"R² Score: {r2:.4f}")
    logger.info(f"RMSE: {rmse:.4f}")
    logger.info(f"MAE: {mae:.4f}")
    logger.info(f"MAPE: {mape:.2f}%")
    
    return results

def main():
    """主函数 - 演示模型使用"""
    from data_loader import create_data_loaders, CONFIGS
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 加载数据
    config = CONFIGS['shenyang']
    train_loader, val_loader, test_loader = create_data_loaders(
        config, 
        batch_size=16, 
        num_workers=0,
        max_images_per_region=5
    )
    
    # 创建模型
    model = StreetViewMLPPredictor(
        feature_dim=128,
        mlp_hidden_dims=[256, 128, 64],
        dropout=0.3
    )
    
    # 创建训练器
    trainer = MLPTrainer(model, device=device, learning_rate=0.001)
    
    # 训练模型
    best_model_path = trainer.train(
        train_loader, 
        val_loader, 
        epochs=50,  # 为了快速测试，减少epoch数
        save_dir='./saved_models'
    )
    
    # 绘制训练历史
    trainer.plot_training_history('mlp_training_history.png')
    
    # 测试模型
    test_results = test_model(best_model_path, test_loader, device)
    
    # 保存结果
    results_path = 'mlp_test_results.json'
    with open(results_path, 'w') as f:
        # 将numpy数组转换为列表以便JSON序列化
        json_results = {
            'model_type': test_results['model_type'],
            'r2_score': test_results['r2_score'],
            'rmse': test_results['rmse'],
            'mae': test_results['mae'],
            'mape': test_results['mape'],
            'predictions': [float(x) for x in test_results['predictions']],
            'targets': [float(x) for x in test_results['targets']],
            'region_ids': [int(x) for x in test_results['region_ids']]
        }
        json.dump(json_results, f, indent=2)
    
    logger.info(f"测试结果保存至: {results_path}")

if __name__ == "__main__":
    main()
