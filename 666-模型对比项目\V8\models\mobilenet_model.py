"""
MobileNet模型 - 优化版本
论文: MobileNets: Efficient Convolutional Neural Networks for Mobile Vision Applications
链接: https://arxiv.org/abs/1704.04861

优化要点:
1. 改进特征提取网络结构
2. 优化回归头设计
3. 使用更好的损失函数和优化器
4. 添加数据增强和正则化
5. 改进训练策略
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import os
import json
from datetime import datetime
import logging
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import matplotlib.pyplot as plt
from torchvision import models
import torch.nn.functional as F

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImprovedMobileNetFeatureExtractor(nn.Module):
    """改进的MobileNet特征提取器"""
    
    def __init__(self, output_dim=128, dropout=0.3, pretrained=True):
        super(ImprovedMobileNetFeatureExtractor, self).__init__()
        
        # 加载预训练的MobileNet V2
        self.backbone = models.mobilenet_v2(pretrained=pretrained)
        
        # 移除分类器，保留特征提取部分
        self.features = self.backbone.features
        
        # 获取最后一层的特征维度
        feature_dim = 1280  # MobileNet V2的最后一层特征维度
        
        # 改进的特征头 - 使用更深的网络
        self.feature_head = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            
            # 第一个特征变换块
            nn.Linear(feature_dim, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            
            # 第二个特征变换块
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout * 0.7),
            
            # 最终特征层
            nn.Linear(256, output_dim),
            nn.BatchNorm1d(output_dim),
            nn.ReLU(inplace=True)
        )
        
        # 改进的回归头 - 多层设计
        self.regression_head = nn.Sequential(
            # 第一层
            nn.Linear(output_dim, 64),
            nn.BatchNorm1d(64),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout * 0.5),
            
            # 第二层
            nn.Linear(64, 32),
            nn.BatchNorm1d(32),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout * 0.3),
            
            # 输出层
            nn.Linear(32, 1)
        )
        
        # 初始化新添加的层
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化权重"""
        for m in [self.feature_head, self.regression_head]:
            for layer in m:
                if isinstance(layer, nn.Linear):
                    # 使用He初始化
                    nn.init.kaiming_normal_(layer.weight, mode='fan_out', nonlinearity='relu')
                    if layer.bias is not None:
                        nn.init.constant_(layer.bias, 0)
                elif isinstance(layer, nn.BatchNorm1d):
                    nn.init.constant_(layer.weight, 1)
                    nn.init.constant_(layer.bias, 0)
    
    def forward(self, x):
        # 特征提取
        x = self.features(x)
        
        # 生成嵌入特征
        embeddings = self.feature_head(x)
        
        # 回归预测
        output = self.regression_head(embeddings)
        output = output.view(-1)  # 确保输出形状正确
        
        return output, embeddings

class HuberLoss(nn.Module):
    """Huber损失函数 - 对异常值更鲁棒"""
    def __init__(self, delta=1.0):
        super(HuberLoss, self).__init__()
        self.delta = delta
    
    def forward(self, input, target):
        abs_error = torch.abs(input - target)
        quadratic = torch.clamp(abs_error, max=self.delta)
        linear = abs_error - quadratic
        return torch.mean(0.5 * quadratic**2 + self.delta * linear)

class ImprovedMobileNetTrainer:
    """改进的MobileNet训练器"""
    
    def __init__(self, model, device='cuda', learning_rate=0.001, weight_decay=1e-4):
        self.model = model.to(device)
        self.device = device
        
        # 使用Huber损失，对异常值更鲁棒
        self.criterion = HuberLoss(delta=0.5)
        
        # 使用AdamW优化器 + 学习率预热
        self.optimizer = optim.AdamW(
            model.parameters(), 
            lr=learning_rate, 
            weight_decay=weight_decay,
            betas=(0.9, 0.999),
            eps=1e-8
        )
        
        # 改进的学习率调度策略
        self.scheduler = optim.lr_scheduler.OneCycleLR(
            self.optimizer,
            max_lr=learning_rate * 10,
            epochs=50,  # 默认epochs
            steps_per_epoch=100,  # 会在训练时更新
            pct_start=0.1,  # 预热阶段占比
            anneal_strategy='cos'
        )
        
        # 训练历史
        self.train_losses = []
        self.val_losses = []
        self.train_r2_scores = []
        self.val_r2_scores = []
        self.learning_rates = []
        
        # 早停参数
        self.best_val_loss = float('inf')
        self.patience = 15
        self.patience_counter = 0
    
    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        predictions = []
        targets = []
        batch_count = 0
        
        for batch_idx, batch in enumerate(train_loader):
            try:
                images = batch['image'].to(self.device, non_blocking=True)
                energy = batch['energy'].to(self.device, non_blocking=True)
                
                self.optimizer.zero_grad()
                outputs, _ = self.model(images)
                
                # 计算损失
                loss = self.criterion(outputs, energy)
                
                # 反向传播
                loss.backward()
                
                # 梯度裁剪防止梯度爆炸
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                
                self.optimizer.step()
                self.scheduler.step()  # OneCycleLR需要每个batch都调用
                
                total_loss += loss.item()
                batch_count += 1
                
                # 收集预测结果
                pred_list = outputs.detach().cpu().numpy()
                target_list = energy.detach().cpu().numpy()
                
                if pred_list.ndim == 0:
                    pred_list = [pred_list.item()]
                if target_list.ndim == 0:
                    target_list = [target_list.item()]
                    
                predictions.extend(pred_list)
                targets.extend(target_list)
                
                if batch_idx % 10 == 0:
                    current_lr = self.optimizer.param_groups[0]['lr']
                    logger.info(f'批次 {batch_idx}/{len(train_loader)}, 损失: {loss.item():.4f}, LR: {current_lr:.6f}')
                    
            except Exception as e:
                logger.warning(f"训练批次 {batch_idx} 失败: {e}")
                continue
        
        if batch_count == 0:
            return 0, 0
        
        avg_loss = total_loss / batch_count
        
        # 安全计算R²分数
        try:
            if len(set(targets)) > 1 and len(predictions) > 1:
                r2 = r2_score(targets, predictions)
            else:
                r2 = 0.0
        except Exception as e:
            logger.warning(f"R²计算失败: {e}")
            r2 = 0.0
        
        return avg_loss, r2
    
    def validate(self, val_loader):
        """验证模型"""
        self.model.eval()
        total_loss = 0
        predictions = []
        targets = []
        batch_count = 0
        
        with torch.no_grad():
            for batch in val_loader:
                try:
                    images = batch['image'].to(self.device, non_blocking=True)
                    energy = batch['energy'].to(self.device, non_blocking=True)
                    
                    outputs, _ = self.model(images)
                    loss = self.criterion(outputs, energy)
                    
                    total_loss += loss.item()
                    batch_count += 1
                    
                    # 收集预测结果
                    pred_list = outputs.cpu().numpy()
                    target_list = energy.cpu().numpy()
                    
                    if pred_list.ndim == 0:
                        pred_list = [pred_list.item()]
                    if target_list.ndim == 0:
                        target_list = [target_list.item()]
                        
                    predictions.extend(pred_list)
                    targets.extend(target_list)
                    
                except Exception as e:
                    logger.warning(f"验证批次失败: {e}")
                    continue
        
        if batch_count == 0:
            return float('inf'), 0, float('inf'), float('inf'), [], []
        
        avg_loss = total_loss / batch_count
        
        # 计算评估指标
        try:
            if len(set(targets)) > 1 and len(predictions) > 1:
                r2 = r2_score(targets, predictions)
                rmse = np.sqrt(mean_squared_error(targets, predictions))
                mae = mean_absolute_error(targets, predictions)
            else:
                r2 = 0.0
                rmse = float('inf')
                mae = float('inf')
        except Exception as e:
            logger.warning(f"指标计算失败: {e}")
            r2 = 0.0
            rmse = float('inf')
            mae = float('inf')
        
        return avg_loss, r2, rmse, mae, predictions, targets
    
    def train(self, train_loader, val_loader, epochs=50, save_dir='./results/saved_models'):
        """训练模型"""
        os.makedirs(save_dir, exist_ok=True)
        best_model_path = None
        
        # 更新学习率调度器的步数
        self.scheduler = optim.lr_scheduler.OneCycleLR(
            self.optimizer,
            max_lr=self.optimizer.param_groups[0]['lr'] * 10,
            epochs=epochs,
            steps_per_epoch=len(train_loader),
            pct_start=0.1,
            anneal_strategy='cos'
        )
        
        logger.info(f"开始训练MobileNet模型，总共 {epochs} 个epoch")
        
        for epoch in range(epochs):
            # 记录当前学习率
            current_lr = self.optimizer.param_groups[0]['lr']
            self.learning_rates.append(current_lr)
            
            # 训练
            train_loss, train_r2 = self.train_epoch(train_loader)
            
            # 验证
            val_loss, val_r2, val_rmse, val_mae, _, _ = self.validate(val_loader)
            
            # 记录历史
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.train_r2_scores.append(train_r2)
            self.val_r2_scores.append(val_r2)
            
            logger.info(f'Epoch {epoch+1}/{epochs}:')
            logger.info(f'  学习率: {current_lr:.6f}')
            logger.info(f'  训练损失: {train_loss:.4f}, 训练R²: {train_r2:.4f}')
            logger.info(f'  验证损失: {val_loss:.4f}, 验证R²: {val_r2:.4f}')
            logger.info(f'  验证RMSE: {val_rmse:.4f}, 验证MAE: {val_mae:.4f}')
            
            # 早停和模型保存
            if val_loss < self.best_val_loss and not np.isnan(val_loss) and not np.isinf(val_loss):
                self.best_val_loss = val_loss
                self.patience_counter = 0
                best_model_path = os.path.join(save_dir, 'shenyang_MobileNet.pt')
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'val_loss': val_loss,
                    'val_r2': val_r2,
                    'train_history': {
                        'train_losses': self.train_losses,
                        'val_losses': self.val_losses,
                        'train_r2_scores': self.train_r2_scores,
                        'val_r2_scores': self.val_r2_scores
                    },
                    'config': {
                        'model_type': 'MobileNet',
                        'output_dim': 128,
                        'dropout': 0.3,
                        'pretrained': True,
                        'architecture': 'ImprovedMobileNetV2'
                    }
                }, best_model_path)
                logger.info(f'保存最佳模型: {best_model_path} (验证损失: {val_loss:.4f})')
            else:
                self.patience_counter += 1
                if self.patience_counter >= self.patience:
                    logger.info(f'早停: 验证损失在 {self.patience} 个epoch内未改善')
                    break
        
        logger.info(f"训练完成! 最佳验证损失: {self.best_val_loss:.4f}")
        return best_model_path

def test_model(model_path, test_loader, device='cuda'):
    """测试模型"""
    try:
        checkpoint = torch.load(model_path, map_location=device)
        config = checkpoint['config']
        
        model = ImprovedMobileNetFeatureExtractor(
            output_dim=config.get('output_dim', 128),
            dropout=0.0,  # 测试时不使用dropout
            pretrained=False  # 测试时不需要预训练权重
        ).to(device)
        
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        predictions = []
        targets = []
        region_ids = []
        
        with torch.no_grad():
            for batch in test_loader:
                try:
                    images = batch['image'].to(device, non_blocking=True)
                    energy = batch['energy'].to(device, non_blocking=True)
                    batch_region_ids = batch['region_id']
                    
                    outputs, _ = model(images)
                    
                    # 安全地收集结果
                    pred_array = outputs.cpu().numpy()
                    target_array = energy.cpu().numpy()
                    
                    if pred_array.ndim == 0:
                        pred_array = [pred_array.item()]
                    if target_array.ndim == 0:
                        target_array = [target_array.item()]
                    
                    predictions.extend(pred_array)
                    targets.extend(target_array)
                    
                    # 处理region_ids
                    if torch.is_tensor(batch_region_ids):
                        region_array = batch_region_ids.cpu().numpy()
                        if region_array.ndim == 0:
                            region_array = [region_array.item()]
                        region_ids.extend(region_array)
                    else:
                        if isinstance(batch_region_ids, (list, tuple)):
                            region_ids.extend(batch_region_ids)
                        else:
                            region_ids.append(batch_region_ids)
                            
                except Exception as e:
                    logger.warning(f"测试批次失败: {e}")
                    continue
        
        # 计算指标
        if len(predictions) == 0 or len(targets) == 0:
            logger.error("没有有效的预测结果")
            return None
        
        try:
            r2 = r2_score(targets, predictions)
            rmse = np.sqrt(mean_squared_error(targets, predictions))
            mae = mean_absolute_error(targets, predictions)
            
            # 安全地计算MAPE
            targets_array = np.array(targets)
            predictions_array = np.array(predictions)
            non_zero_mask = np.abs(targets_array) > 1e-8  # 避免除零
            if np.any(non_zero_mask):
                mape = np.mean(np.abs((targets_array[non_zero_mask] - predictions_array[non_zero_mask]) / targets_array[non_zero_mask])) * 100
            else:
                mape = float('inf')
                
        except Exception as e:
            logger.error(f"指标计算失败: {e}")
            r2 = 0.0
            rmse = float('inf')
            mae = float('inf')
            mape = float('inf')
        
        results = {
            'model_type': 'MobileNet',
            'r2_score': float(r2),
            'rmse': float(rmse),
            'mae': float(mae),
            'mape': float(mape),
            'predictions': [float(x) for x in predictions],
            'targets': [float(x) for x in targets],
            'region_ids': [int(x) for x in region_ids]
        }
        
        logger.info("=== MobileNet模型测试结果 ===")
        logger.info(f"样本数量: {len(predictions)}")
        logger.info(f"R² Score: {r2:.4f}")
        logger.info(f"RMSE: {rmse:.4f}")
        logger.info(f"MAE: {mae:.4f}")
        logger.info(f"MAPE: {mape:.2f}%")
        
        return results
        
    except Exception as e:
        logger.error(f"模型测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None

def main():
    """主函数 - 演示改进的MobileNet模型使用"""
    from data_loader import create_data_loaders, CONFIGS
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 显存优化设置
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        logger.info(f"GPU显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    try:
        # 创建改进的MobileNet模型
        model = ImprovedMobileNetFeatureExtractor(
            output_dim=128,
            dropout=0.3,
            pretrained=True
        )
        
        # 打印模型信息
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        logger.info(f"模型参数总数: {total_params:,}")
        logger.info(f"可训练参数: {trainable_params:,}")
        logger.info(f"模型大小: {total_params * 4 / 1024 / 1024:.1f} MB")
        
        # 加载数据
        config = CONFIGS['shenyang']
        train_loader, val_loader, test_loader = create_data_loaders(
            config, 
            batch_size=8,
            num_workers=0,
            max_images_per_region=3
        )
        
        # 创建训练器
        trainer = ImprovedMobileNetTrainer(
            model, 
            device=device, 
            learning_rate=0.001,
            weight_decay=1e-4
        )
        
        # 训练模型
        best_model_path = trainer.train(
            train_loader, 
            val_loader, 
            epochs=50,
            save_dir='./results/saved_models'
        )
        
        # 测试模型
        if best_model_path:
            test_results = test_model(best_model_path, test_loader, device)
            
            if test_results:
                # 保存结果
                os.makedirs('results/test_results', exist_ok=True)
                results_path = 'results/test_results/mobilenet_test_results.json'
                with open(results_path, 'w') as f:
                    json.dump(test_results, f, indent=2)
                
                logger.info(f"测试结果保存至: {results_path}")
            else:
                logger.error("测试失败")
        else:
            logger.error("训练失败，无法进行测试")
    
    except Exception as e:
        logger.error(f"主程序执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()