"""
ConvNeXt模型
论文: A ConvNet for the 2020s
链接: https://arxiv.org/abs/2201.03545

ConvNeXt是Facebook在2022年提出的现代化卷积网络，
通过借鉴Transformer的设计理念改进传统CNN，达到了与Transformer相当的性能
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from timm.models.layers import trunc_normal_, DropPath
import logging
import os
import sys
import json
from datetime import datetime
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import matplotlib.pyplot as plt

logger = logging.getLogger(__name__)

class LayerNorm(nn.Module):
    """支持两种数据格式的LayerNorm: channels_last (default) 或 channels_first"""
    
    def __init__(self, normalized_shape, eps=1e-6, data_format="channels_last"):
        super().__init__()
        self.weight = nn.Parameter(torch.ones(normalized_shape))
        self.bias = nn.Parameter(torch.zeros(normalized_shape))
        self.eps = eps
        self.data_format = data_format
        self.normalized_shape = (normalized_shape, )
    
    def forward(self, x):
        if self.data_format == "channels_last":
            return F.layer_norm(x, self.normalized_shape, self.weight, self.bias, self.eps)
        elif self.data_format == "channels_first":
            u = x.mean(1, keepdim=True)
            s = (x - u).pow(2).mean(1, keepdim=True)
            x = (x - u) / torch.sqrt(s + self.eps)
            x = self.weight[:, None, None] * x + self.bias[:, None, None]
            return x

class ConvNeXtBlock(nn.Module):
    """ConvNeXt Block
    
    借鉴了Transformer的设计:
    - 使用更大的卷积核(7x7)
    - 反转的瓶颈结构
    - GELU激活函数
    - 更少的激活函数和归一化层
    """
    
    def __init__(self, dim, drop_path=0., layer_scale_init_value=1e-6):
        super().__init__()
        
        # 深度可分离卷积 (DwConv)
        self.dwconv = nn.Conv2d(dim, dim, kernel_size=7, padding=3, groups=dim)
        self.norm = LayerNorm(dim, eps=1e-6)
        
        # 逐点卷积用于通道混合
        self.pwconv1 = nn.Linear(dim, 4 * dim)  # 扩展率为4
        self.act = nn.GELU()
        self.pwconv2 = nn.Linear(4 * dim, dim)
        
        # Layer Scale
        self.gamma = nn.Parameter(layer_scale_init_value * torch.ones((dim)), 
                                 requires_grad=True) if layer_scale_init_value > 0 else None
        
        # Stochastic Depth
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
    
    def forward(self, x):
        input = x
        
        # 深度卷积
        x = self.dwconv(x)
        x = x.permute(0, 2, 3, 1)  # (N, C, H, W) -> (N, H, W, C)
        
        # 归一化
        x = self.norm(x)
        
        # 通道混合
        x = self.pwconv1(x)
        x = self.act(x)
        x = self.pwconv2(x)
        
        # Layer Scale
        if self.gamma is not None:
            x = self.gamma * x
        
        x = x.permute(0, 3, 1, 2)  # (N, H, W, C) -> (N, C, H, W)
        
        # 残差连接
        x = input + self.drop_path(x)
        return x

class ConvNeXtStage(nn.Module):
    """ConvNeXt阶段 - 包含下采样和多个ConvNeXt块"""
    
    def __init__(self, in_channels, out_channels, depth, drop_path_rates):
        super().__init__()
        
        # 下采样层
        self.downsample = nn.Sequential(
            LayerNorm(in_channels, eps=1e-6, data_format="channels_first"),
            nn.Conv2d(in_channels, out_channels, kernel_size=2, stride=2),
        ) if in_channels != out_channels else nn.Identity()
        
        # ConvNeXt块
        self.blocks = nn.ModuleList([
            ConvNeXtBlock(
                dim=out_channels,
                drop_path=drop_path_rates[i]
            ) for i in range(depth)
        ])
    
    def forward(self, x):
        x = self.downsample(x)
        for block in self.blocks:
            x = block(x)
        return x

class ConvNeXtEnergyPredictor(nn.Module):
    """ConvNeXt能耗预测模型"""
    
    def __init__(self, in_chans=3, depths=[3, 3, 9, 3], dims=[96, 192, 384, 768],
                 drop_path_rate=0.1, head_dropout=0.2, output_dim=128):
        super().__init__()
        
        self.model_name = "ConvNeXt"
        
        # Stem层 - 激进的下采样
        self.stem = nn.Sequential(
            nn.Conv2d(in_chans, dims[0], kernel_size=4, stride=4),
            LayerNorm(dims[0], eps=1e-6, data_format="channels_first")
        )
        
        # Stochastic Depth衰减规则
        dp_rates = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]
        
        # 构建ConvNeXt阶段
        self.stages = nn.ModuleList()
        cur = 0
        for i in range(len(depths)):
            in_dim = dims[i-1] if i > 0 else dims[0]
            stage = ConvNeXtStage(
                in_channels=in_dim,
                out_channels=dims[i],
                depth=depths[i],
                drop_path_rates=dp_rates[cur:cur+depths[i]]
            )
            self.stages.append(stage)
            cur += depths[i]
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool2d(1)
        
        # 分类头 - 用于能耗预测
        self.norm = LayerNorm(dims[-1], eps=1e-6)
        
        # 特征投影
        self.feature_projector = nn.Sequential(
            nn.Linear(dims[-1], dims[-1] // 2),
            nn.GELU(),
            nn.Dropout(head_dropout),
            nn.Linear(dims[-1] // 2, output_dim),
            nn.ReLU()
        )
        
        # 回归头
        self.regression_head = nn.Sequential(
            nn.Linear(output_dim, 64),
            nn.LayerNorm(64),
            nn.GELU(),
            nn.Dropout(head_dropout),
            nn.Linear(64, 32),
            nn.GELU(),
            nn.Linear(32, 1)
        )
        
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                trunc_normal_(m.weight, std=.02)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                trunc_normal_(m.weight, std=.02)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, (nn.LayerNorm, LayerNorm)):
                nn.init.constant_(m.weight, 1.0)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        # Stem层
        x = self.stem(x)
        
        # ConvNeXt阶段
        for stage in self.stages:
            x = stage(x)
        
        # 全局池化
        x = self.global_pool(x)
        x = x.flatten(1)
        x = self.norm(x)
        
        # 特征提取
        features = self.feature_projector(x)
        
        # 回归预测
        output = self.regression_head(features)
        output = output.squeeze(-1)
        
        return output, features
    
    def count_parameters(self):
        total = sum(p.numel() for p in self.parameters())
        trainable = sum(p.numel() for p in self.parameters() if p.requires_grad)
        return total, trainable

class ConvNeXtTiny(ConvNeXtEnergyPredictor):
    """ConvNeXt-Tiny配置 (28M参数)"""
    def __init__(self, **kwargs):
        super().__init__(
            depths=[3, 3, 9, 3],
            dims=[96, 192, 384, 768],
            **kwargs
        )
        self.model_name = "ConvNeXt-Tiny"

class ConvNeXtSmall(ConvNeXtEnergyPredictor):
    """ConvNeXt-Small配置 (50M参数)"""
    def __init__(self, **kwargs):
        super().__init__(
            depths=[3, 3, 27, 3],
            dims=[96, 192, 384, 768],
            **kwargs
        )
        self.model_name = "ConvNeXt-Small"

class ConvNeXtPico(ConvNeXtEnergyPredictor):
    """ConvNeXt-Pico配置 (适合GTX 1050的超小型版本)"""
    def __init__(self, **kwargs):
        super().__init__(
            depths=[2, 2, 6, 2],
            dims=[64, 128, 256, 512],
            drop_path_rate=0.05,
            **kwargs
        )
        self.model_name = "ConvNeXt-Pico"

def create_convnext_model(model_size='pico', pretrained=False, **kwargs):
    """创建ConvNeXt模型"""
    
    if model_size == 'tiny':
        model = ConvNeXtTiny(**kwargs)
    elif model_size == 'small':
        model = ConvNeXtSmall(**kwargs)
    elif model_size == 'pico':
        model = ConvNeXtPico(**kwargs)
    else:
        raise ValueError(f"不支持的模型大小: {model_size}")
    
    # 如果需要预训练权重，这里可以加载
    # 注意：由于这是自定义的回归头，可能需要只加载backbone部分
    
    return model

class ConvNeXtTrainer:
    """ConvNeXt训练器"""
    
    def __init__(self, model, device='cuda', learning_rate=0.0001, weight_decay=0.01):
        self.model = model.to(device)
        self.device = device
        self.criterion = nn.MSELoss()
        self.optimizer = torch.optim.AdamW(
            model.parameters(), 
            lr=learning_rate, 
            weight_decay=weight_decay
        )
        self.scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer, T_max=50, eta_min=1e-6
        )
        
        self.train_losses = []
        self.val_losses = []
        self.train_r2_scores = []
        self.val_r2_scores = []
    
    def train_epoch(self, train_loader):
        self.model.train()
        total_loss = 0
        predictions = []
        targets = []
        
        for batch_idx, batch in enumerate(train_loader):
            images = batch['image'].to(self.device)
            energy = batch['energy'].to(self.device)
            
            self.optimizer.zero_grad()
            outputs, _ = self.model(images)
            loss = self.criterion(outputs, energy)
            loss.backward()
            self.optimizer.step()
            
            total_loss += loss.item()
            predictions.extend(outputs.detach().cpu().numpy())
            targets.extend(energy.detach().cpu().numpy())
            
            if batch_idx % 10 == 0:
                logger.info(f'训练批次 {batch_idx}/{len(train_loader)}, 损失: {loss.item():.4f}')
        
        avg_loss = total_loss / len(train_loader)
        r2 = r2_score(targets, predictions)
        
        return avg_loss, r2
    
    def validate(self, val_loader):
        self.model.eval()
        total_loss = 0
        predictions = []
        targets = []
        
        with torch.no_grad():
            for batch in val_loader:
                images = batch['image'].to(self.device)
                energy = batch['energy'].to(self.device)
                
                outputs, _ = self.model(images)
                loss = self.criterion(outputs, energy)
                
                total_loss += loss.item()
                predictions.extend(outputs.cpu().numpy())
                targets.extend(energy.cpu().numpy())
        
        avg_loss = total_loss / len(val_loader)
        r2 = r2_score(targets, predictions)
        rmse = np.sqrt(mean_squared_error(targets, predictions))
        mae = mean_absolute_error(targets, predictions)
        
        return avg_loss, r2, rmse, mae, predictions, targets
    
    def train(self, train_loader, val_loader, epochs=50, save_dir='./results/saved_models', patience=15):
        os.makedirs(save_dir, exist_ok=True)
        best_val_loss = float('inf')
        best_model_path = None
        patience_counter = 0
        
        logger.info(f"开始训练ConvNeXt模型，总共 {epochs} 个epoch")
        
        for epoch in range(epochs):
            # 训练
            train_loss, train_r2 = self.train_epoch(train_loader)
            
            # 验证
            val_loss, val_r2, val_rmse, val_mae, _, _ = self.validate(val_loader)
            
            # 学习率调度
            self.scheduler.step()
            
            # 记录历史
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.train_r2_scores.append(train_r2)
            self.val_r2_scores.append(val_r2)
            
            logger.info(f'Epoch {epoch+1}/{epochs}:')
            logger.info(f'  训练损失: {train_loss:.4f}, 训练R²: {train_r2:.4f}')
            logger.info(f'  验证损失: {val_loss:.4f}, 验证R²: {val_r2:.4f}')
            logger.info(f'  验证RMSE: {val_rmse:.4f}, 验证MAE: {val_mae:.4f}')
            
            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                best_model_path = os.path.join(save_dir, 'shenyang_ConvNeXt.pt')
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'val_loss': val_loss,
                    'val_r2': val_r2,
                    'config': {
                        'model_type': 'ConvNeXt',
                        'model_size': 'pico',
                        'output_dim': 128,
                        'dropout': 0.2
                    }
                }, best_model_path)
                logger.info(f'保存最佳模型: {best_model_path}')
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    logger.info(f'早停: 验证损失在 {patience} 个epoch内未改善')
                    break
        
        logger.info(f"训练完成! 最佳验证损失: {best_val_loss:.4f}")
        return best_model_path
    
    def plot_training_curves(self, save_path='convnext_training_history.png'):
        """绘制训练曲线"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # 损失曲线
        ax1.plot(self.train_losses, label='训练损失')
        ax1.plot(self.val_losses, label='验证损失')
        ax1.set_title('训练和验证损失')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True)
        
        # R²分数
        ax2.plot(self.train_r2_scores, label='训练R²')
        ax2.plot(self.val_r2_scores, label='验证R²')
        ax2.set_title('训练和验证R²分数')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('R² Score')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig(save_path)
        plt.close()
        logger.info(f"训练曲线保存至: {save_path}")

def test_model(model_class, model_path, test_loader, device='cuda'):
    """测试模型"""
    # 加载模型
    checkpoint = torch.load(model_path, map_location=device)
    model = model_class()
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()
    
    predictions = []
    targets = []
    region_ids = []
    
    with torch.no_grad():
        for batch in test_loader:
            images = batch['image'].to(device)
            energy = batch['energy'].to(device)
            batch_region_ids = batch['region_id']
            
            outputs, _ = model(images)
            
            predictions.extend(outputs.cpu().numpy())
            targets.extend(energy.cpu().numpy())
            region_ids.extend(batch_region_ids.numpy() if torch.is_tensor(batch_region_ids) else batch_region_ids)
    
    # 计算指标
    r2 = r2_score(targets, predictions)
    rmse = np.sqrt(mean_squared_error(targets, predictions))
    mae = mean_absolute_error(targets, predictions)
    mape = np.mean(np.abs((np.array(targets) - np.array(predictions)) / np.array(targets))) * 100
    
    results = {
        'model_type': 'ConvNeXt',
        'r2_score': float(r2),  # 确保是Python float
        'rmse': float(rmse),
        'mae': float(mae),
        'mape': float(mape),
        'predictions': [float(x) for x in predictions],
        'targets': [float(x) for x in targets],
        'region_ids': [int(x) for x in region_ids]
    }
    
    logger.info("=== ConvNeXt模型测试结果 ===")
    logger.info(f"R² Score: {r2:.4f}")
    logger.info(f"RMSE: {rmse:.4f}")
    logger.info(f"MAE: {mae:.4f}")
    logger.info(f"MAPE: {mape:.2f}%")
    
    return results

def main():
    """测试ConvNeXt模型"""
    from data_loader import create_data_loaders, CONFIGS
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 创建模型 - 使用Pico版本以适应GTX 1050
    model = create_convnext_model(model_size='pico', output_dim=128, head_dropout=0.2)
    
    # 打印模型信息
    total_params, trainable_params = model.count_parameters()
    logger.info(f"模型: {model.model_name}")
    logger.info(f"总参数: {total_params:,}")
    logger.info(f"可训练参数: {trainable_params:,}")
    
    # 测试前向传播
    dummy_input = torch.randn(2, 3, 224, 224).to(device)
    model = model.to(device)
    output, features = model(dummy_input)
    logger.info(f"输出形状: {output.shape}, 特征形状: {features.shape}")
    
    # 加载数据
    config = CONFIGS['shenyang']
    train_loader, val_loader, test_loader = create_data_loaders(
        config,
        batch_size=8,
        num_workers=0,
        max_images_per_region=3
    )
    
    # 创建训练器
    trainer = ConvNeXtTrainer(
        model,
        device=device,
        learning_rate=0.0001,
        weight_decay=0.05
    )
    
    # 训练模型
    best_model_path = trainer.train(
        train_loader,
        val_loader,
        epochs=50,
        save_dir='./results/saved_models',
        patience=15
    )
    
    # 绘制训练曲线
    trainer.plot_training_curves()
    
    # 测试模型
    if best_model_path:
        test_results = test_model(
            model_class=lambda: create_convnext_model(model_size='pico', output_dim=128),
            model_path=best_model_path,
            test_loader=test_loader,
            device=device
        )
        
        # 保存结果
        os.makedirs('results/test_results', exist_ok=True)
        with open('results/test_results/convnext_test_results.json', 'w') as f:
            json.dump(test_results, f, indent=2)

if __name__ == "__main__":
    main()