"""
两阶段训练流程主程序
1. 特征提取阶段：使用预训练模型提取图像特征
2. MLP预测阶段：使用轻量级MLP进行能耗预测
"""

import os
import sys
import argparse
import logging
import torch
import json
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_loader import CONFIGS
from feature_extractor import extract_all_features
from feature_mlp_predictor import train_and_test_feature_mlp

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('two_stage_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TwoStageTrainer:
    """两阶段训练器"""
    
    def __init__(self, config_name='shenyang', device='auto'):
        self.config_name = config_name
        self.config = CONFIGS[config_name]
        
        # 设备选择
        if device == 'auto':
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        logger.info(f"使用配置: {config_name}")
        logger.info(f"使用设备: {self.device}")
        
        # 创建必要的目录
        self._create_directories()
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            'features',
            'saved_models',
            'results/two_stage',
            'plots'
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            logger.info(f"创建目录: {directory}")
    
    def stage1_extract_features(self, model_names=None, feature_dim=1024, max_images_per_region=3):
        """阶段1：特征提取"""
        logger.info("\n" + "="*80)
        logger.info("开始阶段1：特征提取")
        logger.info("="*80)
        
        if model_names is None:
            model_names = ['mobilenet', 'densenet', 'resnet']
        
        logger.info(f"将使用以下模型进行特征提取: {model_names}")
        logger.info(f"特征维度: {feature_dim}")
        logger.info(f"每个区域最大图像数: {max_images_per_region}")
        
        try:
            extract_all_features(
                config=self.config,
                model_names=model_names,
                feature_dim=feature_dim,
                max_images_per_region=max_images_per_region,
                device=self.device
            )
            logger.info("阶段1完成：特征提取成功")
            return True
            
        except Exception as e:
            logger.error(f"阶段1失败：特征提取出错 - {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    
    def stage2_train_mlp(self, model_names=None, feature_dim=1024):
        """阶段2：MLP训练和测试"""
        logger.info("\n" + "="*80)
        logger.info("开始阶段2：MLP训练和测试")
        logger.info("="*80)
        
        if model_names is None:
            model_names = ['mobilenet', 'densenet', 'resnet']
        
        results = {}
        
        for model_name in model_names:
            logger.info(f"\n训练 {model_name} 特征MLP...")
            
            try:
                # 检查特征文件是否存在
                feature_files = [
                    f'features/features_{model_name}_train.pkl',
                    f'features/features_{model_name}_val.pkl',
                    f'features/features_{model_name}_test.pkl'
                ]
                
                missing_files = [f for f in feature_files if not os.path.exists(f)]
                if missing_files:
                    logger.error(f"缺少特征文件: {missing_files}")
                    continue
                
                # 训练和测试
                test_results = train_and_test_feature_mlp(model_name, feature_dim)
                results[model_name] = test_results
                
                logger.info(f"{model_name} 特征MLP训练完成")
                
            except Exception as e:
                logger.error(f"{model_name} 特征MLP训练失败: {e}")
                import traceback
                logger.error(traceback.format_exc())
        
        # 保存所有结果
        if results:
            results_path = f'results/two_stage/two_stage_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            with open(results_path, 'w') as f:
                json.dump(results, f, indent=2)
            logger.info(f"所有结果保存至: {results_path}")
        
        logger.info("阶段2完成：MLP训练和测试")
        return results
    
    def run_full_pipeline(self, model_names=None, feature_dim=1024, max_images_per_region=3, 
                         skip_feature_extraction=False):
        """运行完整的两阶段流程"""
        logger.info("\n" + "="*100)
        logger.info("开始两阶段街景能耗预测训练流程")
        logger.info("="*100)
        
        start_time = datetime.now()
        
        if model_names is None:
            model_names = ['mobilenet', 'densenet', 'resnet']
        
        logger.info(f"配置信息:")
        logger.info(f"  - 数据集: {self.config_name}")
        logger.info(f"  - 模型: {model_names}")
        logger.info(f"  - 特征维度: {feature_dim}")
        logger.info(f"  - 每区域图像数: {max_images_per_region}")
        logger.info(f"  - 设备: {self.device}")
        logger.info(f"  - 跳过特征提取: {skip_feature_extraction}")
        
        # 阶段1：特征提取
        if not skip_feature_extraction:
            success = self.stage1_extract_features(
                model_names=model_names,
                feature_dim=feature_dim,
                max_images_per_region=max_images_per_region
            )
            if not success:
                logger.error("特征提取失败，终止流程")
                return None
        else:
            logger.info("跳过特征提取阶段")
        
        # 阶段2：MLP训练
        results = self.stage2_train_mlp(
            model_names=model_names,
            feature_dim=feature_dim
        )
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info("\n" + "="*100)
        logger.info("两阶段训练流程完成")
        logger.info(f"总耗时: {duration}")
        logger.info("="*100)
        
        # 打印结果摘要
        if results:
            logger.info("\n结果摘要:")
            for model_name, result in results.items():
                logger.info(f"{model_name:12} - R²: {result['r2_score']:.4f}, "
                          f"RMSE: {result['rmse']:.4f}, "
                          f"MAE: {result['mae']:.4f}, "
                          f"MAPE: {result['mape']:.2f}%")
        
        return results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='两阶段街景能耗预测训练')
    
    parser.add_argument('--config', default='shenyang', 
                       help='数据集配置名称 (默认: shenyang)')
    parser.add_argument('--models', nargs='+', 
                       default=['mobilenet', 'densenet', 'resnet'],
                       help='要使用的模型列表')
    parser.add_argument('--feature-dim', type=int, default=1024,
                       help='特征维度 (默认: 1024)')
    parser.add_argument('--max-images', type=int, default=3,
                       help='每个区域最大图像数 (默认: 3)')
    parser.add_argument('--device', default='auto',
                       help='计算设备 (auto/cuda/cpu, 默认: auto)')
    parser.add_argument('--skip-features', action='store_true',
                       help='跳过特征提取阶段')
    parser.add_argument('--stage', choices=['1', '2', 'all'], default='all',
                       help='运行阶段 (1: 特征提取, 2: MLP训练, all: 全部)')
    
    args = parser.parse_args()
    
    # 创建训练器
    trainer = TwoStageTrainer(config_name=args.config, device=args.device)
    
    # 根据参数运行相应阶段
    if args.stage == '1':
        trainer.stage1_extract_features(
            model_names=args.models,
            feature_dim=args.feature_dim,
            max_images_per_region=args.max_images
        )
    elif args.stage == '2':
        trainer.stage2_train_mlp(
            model_names=args.models,
            feature_dim=args.feature_dim
        )
    else:  # args.stage == 'all'
        trainer.run_full_pipeline(
            model_names=args.models,
            feature_dim=args.feature_dim,
            max_images_per_region=args.max_images,
            skip_feature_extraction=args.skip_features
        )

if __name__ == "__main__":
    main()
