"""
快速修复脚本
修复张量形状和数组迭代问题，然后运行快速测试
"""

import torch
import sys
import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_tensor_operations():
    """测试张量操作是否正确"""
    logger.info("🔧 测试张量操作修复...")
    
    # 模拟批次数据
    batch_sizes = [1, 4, 8]
    
    for batch_size in batch_sizes:
        # 模拟模型输出
        model_output = torch.randn(batch_size, 1)  # 原始输出 [batch_size, 1]
        target = torch.randn(batch_size)           # 目标 [batch_size]
        
        # 测试新的修复方法
        fixed_output = model_output.view(-1)       # 修复后的输出 [batch_size]
        
        # 测试MSE损失
        try:
            loss_fn = torch.nn.MSELoss()
            loss = loss_fn(fixed_output, target)
            logger.info(f"✅ 批次大小 {batch_size}: 损失计算成功 {loss.item():.4f}")
        except Exception as e:
            logger.error(f"❌ 批次大小 {batch_size}: 损失计算失败 {e}")
            return False
        
        # 测试numpy转换
        try:
            pred_list = fixed_output.detach().cpu().numpy().tolist()
            target_list = target.detach().cpu().numpy().tolist()
            
            # 处理单个样本的情况
            if not isinstance(pred_list, list):
                pred_list = [pred_list]
            if not isinstance(target_list, list):
                target_list = [target_list]
            
            logger.info(f"✅ 批次大小 {batch_size}: numpy转换成功, 预测: {len(pred_list)}, 目标: {len(target_list)}")
        except Exception as e:
            logger.error(f"❌ 批次大小 {batch_size}: numpy转换失败 {e}")
            return False
    
    return True

def test_r2_calculation():
    """测试R²分数计算"""
    logger.info("🔧 测试R²分数计算修复...")
    
    from sklearn.metrics import r2_score
    import numpy as np
    
    # 测试数据
    test_cases = [
        ([1.0], [1.1]),           # 单个样本
        ([1.0, 2.0], [1.1, 2.1]), # 两个样本
        ([1.0, 1.0], [1.1, 1.1]), # 相同目标值
        ([1.0, 2.0, 3.0], [1.1, 2.1, 3.1]), # 正常情况
    ]
    
    for i, (targets, predictions) in enumerate(test_cases):
        try:
            if len(set(targets)) > 1 and len(predictions) > 1:
                r2 = r2_score(targets, predictions)
                logger.info(f"✅ 测试案例 {i+1}: R² = {r2:.4f}")
            else:
                r2 = 0.0
                logger.info(f"✅ 测试案例 {i+1}: R² = {r2:.4f} (fallback)")
        except Exception as e:
            logger.error(f"❌ 测试案例 {i+1}: R²计算失败 {e}")
            return False
    
    return True

def run_quick_mobilenet_test():
    """运行MobileNet快速测试"""
    logger.info("🚀 运行MobileNet快速测试...")
    
    try:
        # 导入修复后的模块
        from models.mobilenet_model import MobileNetFeatureExtractor, MobileNetTrainer
        from data_loader import create_data_loaders, CONFIGS
        
        # 设置设备
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"使用设备: {device}")
        
        # 加载数据
        config = CONFIGS['shenyang']
        train_loader, val_loader, test_loader = create_data_loaders(
            config, 
            batch_size=4,  # 小批次测试
            num_workers=0, 
            max_images_per_region=1  # 每个区域只用1张图像
        )
        
        # 创建模型
        model = MobileNetFeatureExtractor(output_dim=64, dropout=0.1)
        trainer = MobileNetTrainer(model, device=device, learning_rate=0.01)
        
        # 训练1个epoch测试
        logger.info("测试训练1个epoch...")
        train_loss, train_r2 = trainer.train_epoch(train_loader)
        logger.info(f"训练损失: {train_loss:.4f}, R²: {train_r2:.4f}")
        
        # 验证测试
        logger.info("测试验证...")
        val_loss, val_r2, val_rmse, val_mae, _, _ = trainer.validate(val_loader)
        logger.info(f"验证损失: {val_loss:.4f}, R²: {val_r2:.4f}, RMSE: {val_rmse:.4f}")
        
        logger.info("✅ MobileNet快速测试成功!")
        return True
        
    except Exception as e:
        logger.error(f"❌ MobileNet快速测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 快速修复和测试工具")
    print("="*50)
    
    # 运行测试
    tests = [
        ("张量操作测试", test_tensor_operations),
        ("R²分数计算测试", test_r2_calculation),
        ("MobileNet快速测试", run_quick_mobilenet_test),
    ]
    
    all_passed = True
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 运行 {test_name}...")
        try:
            result = test_func()
            if result:
                logger.info(f"✅ {test_name} 通过")
            else:
                logger.error(f"❌ {test_name} 失败")
                all_passed = False
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {e}")
            all_passed = False
    
    # 总结
    print("\n" + "="*50)
    if all_passed:
        print("🎉 所有测试通过! 现在可以安全运行完整训练:")
        print("   python main_runner.py --single MobileNet --epochs 10")
        print("   python main_runner.py --models MobileNet EfficientNet --epochs 20")
    else:
        print("❌ 存在问题，请检查错误信息并修复")
    
    print("="*50)

if __name__ == "__main__":
    main()
