# 🏙️ 街景数据预测街区能耗深度学习项目

基于街景图像数据预测城市街区能耗的深度学习框架，支持多种先进的计算机视觉模型，专为实际应用场景优化。

## 📋 项目概述

本项目旨在利用街景图像数据预测城市街区的能耗水平，为智慧城市建设和能源管理提供数据支持。项目实现了多种深度学习模型，从轻量级的MobileNet到先进的Vision Transformer，适配不同的硬件配置。

### 🎯 主要特点

- **多模型支持**: 实现了5种不同的深度学习模型
- **硬件友好**: 针对GTX 1050等入门级GPU优化
- **完整流程**: 从数据加载到模型训练、测试和对比分析
- **可视化分析**: 丰富的图表和报告生成
- **模块化设计**: 每个模型独立实现，便于维护和扩展

## 📊 数据集结构

### 沈阳市街景能耗数据集

```
data/shenyang/
├── shenyang_region2allinfo.json          # 区域详细信息和能耗标签
├── streetview_image/
│   ├── Region/                           # 街景图像目录
│   │   ├── 150/                         # 区域ID目录
│   │   │   ├── 123.4339224_41.7518699_0_0.jpg
│   │   │   └── ...
│   │   └── .../
│   └── region_5_10_poi_image_filename.json  # POI-图像映射文件
├── shenyang_zl15_train.csv               # 训练集区域ID
├── shenyang_zl15_valid.csv               # 验证集区域ID
└── shenyang_zl15_test.csv                # 测试集区域ID
```

### 数据统计
- **总区域数**: 122个
- **训练集**: 97个区域 (79.5%)
- **验证集**: 4个区域 (3.3%)
- **测试集**: 21个区域 (17.2%)
- **街景图像**: 每个区域约40张图像
- **能耗范围**: 0.000 - 2.566

## 🤖 模型清单

### 1. 轻量级模型 (适合GTX 1050)

#### MobileNet-V2
- **文件**: `models/mobilenet_model.py`
- **论文**: [MobileNets: Efficient CNNs for Mobile Vision Applications](https://arxiv.org/abs/1704.04861)
- **特点**: 深度可分离卷积，参数少，速度快
- **显存需求**: < 2GB
- **推荐场景**: 快速原型验证，边缘设备部署

#### MLP (多层感知器)
- **文件**: `models/mlp_model.py`
- **特点**: 基础神经网络，包含图像特征提取器
- **显存需求**: < 1GB
- **推荐场景**: 基线模型，快速测试

### 2. 中等复杂度模型

#### EfficientNet-B0
- **文件**: `models/efficientnet_model.py`
- **论文**: [EfficientNet: Rethinking Model Scaling for CNNs](https://arxiv.org/abs/1905.11946)
- **特点**: 复合缩放优化，性能与效率平衡
- **显存需求**: 2-3GB
- **推荐场景**: 平衡性能和效率

#### ResNet-50
- **文件**: `models/resnet_model.py`
- **论文**: [Deep Residual Learning for Image Recognition](https://arxiv.org/abs/1512.03385)
- **特点**: 残差连接，解决梯度消失问题
- **显存需求**: 3-4GB
- **推荐场景**: 稳定的基准模型

### 3. 高级模型

#### Vision Transformer (轻量级)
- **文件**: `models/vit_model.py`
- **论文**: [An Image is Worth 16x16 Words](https://arxiv.org/abs/2010.11929)
- **特点**: 自注意力机制，全局特征提取
- **显存需求**: 3-4GB (优化版)
- **推荐场景**: 需要全局理解的任务

## ⚙️ 环境配置

### 系统要求

- **操作系统**: Windows 10/11, Linux, macOS
- **Python**: 3.7+
- **GPU**: NVIDIA GTX 1050 或更高 (可选, 支持CPU训练)
- **显存**: 至少2GB (推荐4GB+)
- **内存**: 8GB+ (推荐16GB+)

### 依赖安装

1. **克隆项目**
```bash
git clone <项目地址>
cd streetview-energy-prediction
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **验证安装**
```bash
python -c "import torch; print('PyTorch版本:', torch.__version__)"
python -c "import torch; print('CUDA可用:', torch.cuda.is_available())"
```

### requirements.txt
```
torch>=1.10.0
torchvision>=0.11.0
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0
matplotlib>=3.5.0
seaborn>=0.11.0
Pillow>=8.3.0
tqdm>=4.62.0
```

## 🚀 使用方法

### 快速开始

1. **测试数据加载器**
```bash
python main_runner.py --test_data_loader
```

2. **训练单个模型**
```bash
# 训练MobileNet (推荐用于GTX 1050)
python main_runner.py --single MobileNet

# 训练EfficientNet
python main_runner.py --single EfficientNet

python main_runner.py --single ResNet

python main_runner.py --single ViT
```

3. **训练所有模型**
```bash
python main_runner.py --models MobileNet EfficientNet ResNet --epochs 30
```

4. **仅运行模型对比**
```bash
python main_runner.py --compare_only
```

### 详细使用

#### 单独运行模型
```bash
# 直接运行模型文件
python models/mobilenet_model.py
python models/efficientnet_model.py
python models/resnet_model.py
python models/vit_model.py
python models/mlp_model.py
```

#### 自定义训练参数
```bash
python main_runner.py \
    --models MobileNet EfficientNet \
    --batch_size 8 \
    --epochs 50 \
    --max_images 5
```

#### 生成对比报告
```bash
python model_comparator.py
```

### 参数说明

- `--models`: 要训练的模型列表
- `--batch_size`: 批次大小 (默认根据GPU自动选择)
- `--epochs`: 训练轮数 (默认50)
- `--max_images`: 每个区域最大图像数量 (默认3)
- `--single`: 只训练单个模型
- `--compare_only`: 只运行模型对比，不训练
- `--test_data_loader`: 测试数据加载器

## 🔧 GTX 1050 优化指南

### 推荐配置

1. **首选模型**: MobileNet-V2
   - 显存需求: < 2GB
   - 训练时间: 约1-2小时
   - 性能: 良好的速度和精度平衡

2. **备选模型**: EfficientNet-B0
   - 显存需求: 2-3GB
   - 训练时间: 约2-3小时
   - 性能: 更好的精度

### 优化设置

```python
# 推荐的训练参数
batch_size = 4          # 小批次以节省显存
max_images = 3          # 限制每区域图像数量
num_workers = 0         # Windows上设为0避免多进程问题
```

### 显存优化技巧

1. **减小批次大小**
```bash
python main_runner.py --batch_size 4
```

2. **限制图像数量**
```bash
python main_runner.py --max_images 2
```

3. **使用混合精度训练** (如果支持)
```python
# 在模型文件中启用
torch.cuda.amp.autocast()
```

4. **定期清理显存**
```python
torch.cuda.empty_cache()
```

## 🖥️ 跨设备运行指南

### 从GTX 1050迁移到高端GPU

1. **备份模型和配置**
```bash
cp -r saved_models/ /path/to/backup/
cp *.json /path/to/backup/
```

2. **调整批次大小**
```python
# 高端GPU可以使用更大的批次
batch_size = 16  # RTX 3080
batch_size = 32  # RTX 4090
```

3. **启用更复杂的模型**
```bash
# 在高端设备上训练ViT
python main_runner.py --single ViT --batch_size 16
```

### CPU训练 (备用方案)

```bash
# 强制使用CPU
export CUDA_VISIBLE_DEVICES=""
python models/mobilenet_model.py
```

注意: CPU训练会显著增加训练时间 (10-50倍)

## 📈 结果分析

### 输出文件说明

训练完成后，会生成以下文件：

- `*_test_results.json`: 模型测试结果 (R², RMSE, MAE, MAPE)
- `*_training_history.png`: 训练历史图表
- `model_comparison_report.md`: 详细对比报告
- `model_performance_comparison.png`: 性能对比图
- `saved_models/`: 训练好的模型文件
- `training_log.txt`: 详细训练日志

### 性能指标说明

- **R² Score**: 决定系数，越接近1越好
- **RMSE**: 均方根误差，越小越好
- **MAE**: 平均绝对误差，越小越好
- **MAPE**: 平均绝对百分比误差，越小越好

## 🐛 调试指南

### 常见问题

1. **CUDA内存不足**
```
RuntimeError: CUDA out of memory
```
**解决方案**:
- 减小batch_size
- 减少max_images_per_region
- 使用更小的模型 (MobileNet)

2. **数据加载失败**
```
FileNotFoundError: No such file or directory
```
**解决方案**:
- 检查数据路径配置
- 确保所有数据文件存在
- 运行数据完整性检查

3. **模型训练不收敛**
**解决方案**:
- 调整学习率
- 增加训练轮数
- 检查数据质量

### 调试命令

```bash
# 测试数据加载
python main_runner.py --test_data_loader

# 查看GPU状态
nvidia-smi

# 检查文件完整性
python data_loader.py
```

### 日志级别设置

```python
import logging
logging.basicConfig(level=logging.DEBUG)  # 详细日志
logging.basicConfig(level=logging.INFO)   # 标准日志
logging.basicConfig(level=logging.ERROR)  # 仅错误
```

## 📝 开发指南

### 添加新模型

1. **创建模型文件**
```python
# models/new_model.py
class NewModel(nn.Module):
    def __init__(self):
        # 模型定义
        pass
    
    def forward(self, x):
        # 前向传播
        pass
```

2. **实现训练器**
```python
class NewModelTrainer:
    def train(self):
        # 训练逻辑
        pass
```

3. **添加到主运行器**
```python
# 在main_runner.py中添加
elif model_name.lower() == 'newmodel':
    from models.new_model import main as new_main
    new_main()
```

### 自定义数据集

1. **修改配置**
```python
CONFIGS = {
    'your_dataset': {
        'region2allinfo_path': 'path/to/your/data.json',
        # 其他路径配置
    }
}
```

2. **适配数据加载器**
```python
# 在data_loader.py中修改StreetViewDataset类
```

## 🤝 贡献指南

### 提交规范

1. **Fork项目**
2. **创建特性分支**: `git checkout -b feature/AmazingFeature`
3. **提交更改**: `git commit -m 'Add some AmazingFeature'`
4. **推送分支**: `git push origin feature/AmazingFeature`
5. **创建Pull Request**

### 代码规范

- 遵循PEP 8代码风格
- 添加适当的注释和文档字符串
- 包含单元测试
- 更新README文档

## 📄 许可证

本项目采用MIT许可证 - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

- 感谢PyTorch团队提供优秀的深度学习框架
- 感谢各个模型的原作者提供开源实现
- 感谢沈阳市提供的街景数据集

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 📧 邮箱: [<EMAIL>]
- 🐛 问题反馈: [GitHub Issues](https://github.com/your-repo/issues)
- 📖 项目文档: [Wiki](https://github.com/your-repo/wiki)

---

**最后更新**: 2024年12月

**项目状态**: 🟢 积极维护中

**支持的Python版本**: 3.7, 3.8, 3.9, 3.10, 3.11

**测试状态**: ✅ 在GTX 1050, RTX 3080, RTX 4090上测试通过
