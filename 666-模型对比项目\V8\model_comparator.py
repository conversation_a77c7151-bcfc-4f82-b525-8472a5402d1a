"""
模型对比分析器
用于生成完整的模型性能对比报告和可视化分析
"""

import os
import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import logging

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelComparator:
    """模型对比分析器"""
    
    def __init__(self):
        self.results = {}
        self.model_configs = {
            'MLP': {
                'full_name': '多层感知器',
                'params': '1.0M',
                'type': '基线模型',
                'color': '#FF6B6B'
            },
            'MobileNet': {
                'full_name': 'MobileNet-V2',
                'params': '3.5M',
                'type': '轻量级CNN',
                'color': '#4ECDC4'
            },
            'EfficientNet': {
                'full_name': 'EfficientNet-B0',
                'params': '5.3M',
                'type': '效率优化CNN',
                'color': '#45B7D1'
            },
            'DenseNet': {
                'full_name': 'DenseNet-Compact',
                'params': '2.0M',
                'type': '密集连接CNN',
                'color': '#96CEB4'
            },
            'ConvNeXt': {
                'full_name': 'ConvNeXt-Pico',
                'params': '3.0M',
                'type': '现代化CNN',
                'color': '#FECA57'
            },
            'ResNet': {
                'full_name': 'ResNet-50',
                'params': '25.6M',
                'type': '深度残差网络',
                'color': '#FF9FF3'
            },
            'ResNet-Improved': {
                'full_name': 'ResNet-50 (优化版)',
                'params': '25.6M',
                'type': '优化深度网络',
                'color': '#FF6B9D'
            },
            'ViT': {
                'full_name': 'Vision Transformer',
                'params': '8.0M',
                'type': 'Transformer',
                'color': '#A8E6CF'
            },
            'ViT-Improved': {
                'full_name': 'ViT (优化版)',
                'params': '22M',
                'type': '优化Transformer',
                'color': '#7FCDCD'
            },
            'SwinTransformer': {
                'full_name': 'Swin Transformer',
                'params': '5.0M',
                'type': '层级Transformer',
                'color': '#DDA0DD'
            }
        }
    
    def load_results(self):
        """加载所有模型的测试结果"""
        result_files = {
            'MLP': 'mlp_test_results.json',
            'MobileNet': 'mobilenet_test_results.json',
            'EfficientNet': 'efficientnet_test_results.json',
            'DenseNet': 'densenet_test_results.json',
            'ConvNeXt': 'convnext_test_results.json',
            'ResNet': 'resnet_test_results.json',
            'ResNet-Improved': 'resnet_improved_test_results.json',
            'ViT': 'vit_test_results.json',
            'ViT-Improved': 'vit_improved_test_results.json',
            'SwinTransformer': 'swin_test_results.json'
        }
        
        for model_name, filename in result_files.items():
            if os.path.exists(filename):
                try:
                    with open(filename, 'r') as f:
                        data = json.load(f)
                        self.results[model_name] = data
                        logger.info(f"加载 {model_name} 结果: R²={data.get('r2_score', 0):.3f}")
                except Exception as e:
                    logger.warning(f"加载 {filename} 失败: {e}")
            else:
                logger.warning(f"结果文件不存在: {filename}")
        
        logger.info(f"成功加载 {len(self.results)} 个模型的结果")
        return len(self.results) > 0
    
    def create_performance_dataframe(self):
        """创建性能对比数据框"""
        data = []
        for model_name, result in self.results.items():
            config = self.model_configs.get(model_name, {})
            data.append({
                'Model': model_name,
                'Full_Name': config.get('full_name', model_name),
                'Type': config.get('type', 'Unknown'),
                'Parameters': config.get('params', 'Unknown'),
                'R2_Score': result.get('r2_score', 0),
                'RMSE': result.get('rmse', float('inf')),
                'MAE': result.get('mae', float('inf')),
                'MAPE': result.get('mape', float('inf')),
                'Color': config.get('color', '#888888')
            })
        
        df = pd.DataFrame(data)
        
        # 计算综合评分
        if len(df) > 0:
            # 归一化指标 (R²越大越好，其他越小越好)
            df['R2_Norm'] = df['R2_Score'] / df['R2_Score'].max()
            df['RMSE_Norm'] = df['RMSE'].min() / df['RMSE']
            df['MAE_Norm'] = df['MAE'].min() / df['MAE']
            
            # 综合评分 (权重: R²=0.4, RMSE=0.3, MAE=0.3)
            df['Composite_Score'] = (
                df['R2_Norm'] * 0.4 + 
                df['RMSE_Norm'] * 0.3 + 
                df['MAE_Norm'] * 0.3
            ) * 100
            
            # 排序
            df = df.sort_values('R2_Score', ascending=False).reset_index(drop=True)
            df['Rank'] = range(1, len(df) + 1)
        
        return df
    
    def plot_performance_comparison(self, save_path='model_performance_comparison.png'):
        """绘制性能对比图"""
        df = self.create_performance_dataframe()
        if df.empty:
            logger.warning("没有数据可用于绘制性能对比图")
            return
        
        fig = plt.figure(figsize=(20, 12))
        gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)
        
        # 1. R²分数对比
        ax1 = fig.add_subplot(gs[0, 0])
        bars1 = ax1.bar(range(len(df)), df['R2_Score'], 
                       color=df['Color'], alpha=0.8, edgecolor='black')
        ax1.set_title('R² Score 对比', fontsize=14, fontweight='bold')
        ax1.set_ylabel('R² Score')
        ax1.set_xticks(range(len(df)))
        ax1.set_xticklabels(df['Model'], rotation=45, ha='right')
        ax1.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, (bar, score) in enumerate(zip(bars1, df['R2_Score'])):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{score:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # 2. RMSE对比
        ax2 = fig.add_subplot(gs[0, 1])
        bars2 = ax2.bar(range(len(df)), df['RMSE'], 
                       color=df['Color'], alpha=0.8, edgecolor='black')
        ax2.set_title('RMSE 对比 (越小越好)', fontsize=14, fontweight='bold')
        ax2.set_ylabel('RMSE')
        ax2.set_xticks(range(len(df)))
        ax2.set_xticklabels(df['Model'], rotation=45, ha='right')
        ax2.grid(True, alpha=0.3)
        
        for bar, rmse in zip(bars2, df['RMSE']):
            if rmse != float('inf'):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                        f'{rmse:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # 3. MAE对比
        ax3 = fig.add_subplot(gs[0, 2])
        bars3 = ax3.bar(range(len(df)), df['MAE'], 
                       color=df['Color'], alpha=0.8, edgecolor='black')
        ax3.set_title('MAE 对比 (越小越好)', fontsize=14, fontweight='bold')
        ax3.set_ylabel('MAE')
        ax3.set_xticks(range(len(df)))
        ax3.set_xticklabels(df['Model'], rotation=45, ha='right')
        ax3.grid(True, alpha=0.3)
        
        for bar, mae in zip(bars3, df['MAE']):
            if mae != float('inf'):
                height = bar.get_height()
                ax3.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                        f'{mae:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # 4. 综合评分雷达图
        ax4 = fig.add_subplot(gs[1, :], projection='polar')
        
        # 选择前5个模型绘制雷达图
        top_models = df.head(5)
        metrics = ['R2_Norm', 'RMSE_Norm', 'MAE_Norm']
        metric_labels = ['R² Score', 'RMSE (inv)', 'MAE (inv)']
        
        angles = np.linspace(0, 2*np.pi, len(metrics), endpoint=False).tolist()
        angles += angles[:1]  # 闭合图形
        
        for idx, (_, model) in enumerate(top_models.iterrows()):
            values = [model[metric] for metric in metrics]
            values += values[:1]  # 闭合图形
            
            ax4.plot(angles, values, 'o-', linewidth=2, 
                    label=model['Model'], color=model['Color'])
            ax4.fill(angles, values, alpha=0.25, color=model['Color'])
        
        ax4.set_xticks(angles[:-1])
        ax4.set_xticklabels(metric_labels)
        ax4.set_ylim(0, 1)
        ax4.set_title('Top 5 模型综合性能雷达图', fontsize=14, fontweight='bold', pad=20)
        ax4.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        ax4.grid(True)
        
        # 5. 模型类型分布饼图
        ax5 = fig.add_subplot(gs[2, 0])
        type_counts = df['Type'].value_counts()
        wedges, texts, autotexts = ax5.pie(type_counts.values, labels=type_counts.index, 
                                          autopct='%1.1f%%', startangle=90)
        ax5.set_title('模型类型分布', fontsize=14, fontweight='bold')
        
        # 6. 参数量 vs 性能散点图
        ax6 = fig.add_subplot(gs[2, 1])
        
        # 提取参数量数值
        param_values = []
        for params in df['Parameters']:
            if 'M' in params:
                param_values.append(float(params.replace('M', '')))
            else:
                param_values.append(1.0)  # 默认值
        
        scatter = ax6.scatter(param_values, df['R2_Score'], 
                            c=df['R2_Score'], s=100, alpha=0.7, 
                            cmap='viridis', edgecolors='black')
        
        for i, model in enumerate(df['Model']):
            ax6.annotate(model, (param_values[i], df['R2_Score'].iloc[i]),
                        xytext=(5, 5), textcoords='offset points', fontsize=8)
        
        ax6.set_xlabel('参数量 (M)')
        ax6.set_ylabel('R² Score')
        ax6.set_title('参数量 vs 性能', fontsize=14, fontweight='bold')
        ax6.grid(True, alpha=0.3)
        
        # 添加颜色条
        cbar = plt.colorbar(scatter, ax=ax6)
        cbar.set_label('R² Score', rotation=270, labelpad=15)
        
        # 7. 排行榜表格
        ax7 = fig.add_subplot(gs[2, 2])
        ax7.axis('tight')
        ax7.axis('off')
        
        # 创建排行榜数据
        rank_data = df[['Rank', 'Model', 'R2_Score', 'Composite_Score']].head(8)
        rank_data['R2_Score'] = rank_data['R2_Score'].round(3)
        rank_data['Composite_Score'] = rank_data['Composite_Score'].round(1)
        
        table = ax7.table(cellText=rank_data.values,
                         colLabels=['排名', '模型', 'R²', '综合分'],
                         cellLoc='center',
                         loc='center',
                         bbox=[0, 0, 1, 1])
        
        table.auto_set_font_size(False)
        table.set_fontsize(9)
        table.scale(1, 1.5)
        
        # 设置表格样式
        for (i, j), cell in table.get_celld().items():
            if i == 0:  # 表头
                cell.set_text_props(weight='bold')
                cell.set_facecolor('#4CAF50')
                cell.set_text_props(color='white')
            elif j == 0:  # 排名列
                if i <= 3:  # 前三名
                    colors = ['#FFD700', '#C0C0C0', '#CD7F32']
                    cell.set_facecolor(colors[i-1])
                else:
                    cell.set_facecolor('#E8F5E8')
            else:
                cell.set_facecolor('#F5F5F5')
        
        ax7.set_title('性能排行榜', fontsize=14, fontweight='bold')
        
        plt.suptitle('🏙️ 街景能耗预测模型全面对比分析', fontsize=18, fontweight='bold', y=0.98)
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        logger.info(f"性能对比图保存至: {save_path}")
    
    def plot_prediction_scatter(self, save_path='prediction_scatter_comparison.png'):
        """绘制预测结果散点图对比"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()
        
        model_count = 0
        for model_name, result in self.results.items():
            if model_count >= 6:  # 最多显示6个模型
                break
                
            ax = axes[model_count]
            
            predictions = result.get('predictions', [])
            targets = result.get('targets', [])
            
            if predictions and targets:
                # 散点图
                ax.scatter(targets, predictions, alpha=0.6, s=30,
                          color=self.model_configs.get(model_name, {}).get('color', '#888888'))
                
                # 完美预测线
                min_val = min(min(targets), min(predictions))
                max_val = max(max(targets), max(predictions))
                ax.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, alpha=0.8)
                
                # 添加指标文本
                r2 = result.get('r2_score', 0)
                rmse = result.get('rmse', 0)
                mae = result.get('mae', 0)
                
                info_text = f'R² = {r2:.3f}\nRMSE = {rmse:.3f}\nMAE = {mae:.3f}'
                ax.text(0.05, 0.95, info_text, transform=ax.transAxes, 
                       verticalalignment='top', fontsize=10,
                       bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
                
                ax.set_xlabel('实际能耗值')
                ax.set_ylabel('预测能耗值')
                ax.set_title(f'{model_name} 预测结果', fontweight='bold')
                ax.grid(True, alpha=0.3)
                
                # 设置坐标轴范围相等
                ax.set_aspect('equal', adjustable='box')
            
            model_count += 1
        
        # 隐藏多余的子图
        for i in range(model_count, len(axes)):
            axes[i].set_visible(False)
        
        plt.suptitle('各模型预测结果对比', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        logger.info(f"预测散点图保存至: {save_path}")
    
    def generate_markdown_report(self, save_path='model_comparison_report.md'):
        """生成Markdown格式的对比报告"""
        df = self.create_performance_dataframe()
        
        if df.empty:
            logger.warning("没有数据可用于生成报告")
            return
        
        report = f"""# 🏙️ 街景能耗预测模型对比分析报告

**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**测试数据集**: 沈阳市街景能耗数据集  
**模型数量**: {len(df)} 个  
**评估指标**: R² Score, RMSE, MAE, MAPE

---

## 📊 模型性能排行榜

### 🏆 TOP 10 模型排名

| 排名 | 模型 | R² Score | RMSE | MAE | MAPE(%) | 参数量 | 类型 | 推荐等级 |
|------|------|----------|------|-----|---------|--------|------|----------|
"""
        
        # 添加排行榜数据
        for _, model in df.head(10).iterrows():
            stars = "⭐" * min(5, max(1, int(model['R2_Score'] * 5)))
            report += f"| {model['Rank']} | **{model['Model']}** | {model['R2_Score']:.3f} | {model['RMSE']:.3f} | {model['MAE']:.3f} | {model['MAPE']:.1f} | {model['Parameters']} | {model['Type']} | {stars} |\n"
        
        report += f"""

---

## 📈 详细性能分析

### 🥇 最佳性能模型

**{df.iloc[0]['Model']}** ({df.iloc[0]['Full_Name']})
- **R² Score**: {df.iloc[0]['R2_Score']:.3f} (解释了 {df.iloc[0]['R2_Score']*100:.1f}% 的方差)
- **RMSE**: {df.iloc[0]['RMSE']:.3f}
- **MAE**: {df.iloc[0]['MAE']:.3f}
- **MAPE**: {df.iloc[0]['MAPE']:.1f}%
- **模型类型**: {df.iloc[0]['Type']}
- **参数量**: {df.iloc[0]['Parameters']}

### 🚀 效率之选

"""
        
        # 找出参数量最少但性能还不错的模型
        efficient_models = df[df['R2_Score'] > 0.7].nsmallest(3, 'Parameters')
        for _, model in efficient_models.iterrows():
            report += f"""
**{model['Model']}** - 高效能模型
- R² Score: {model['R2_Score']:.3f} | 参数量: {model['Parameters']} | 效率评分: {model['Composite_Score']:.1f}/100
"""
        
        report += f"""

---

## 🎯 模型选择建议

### 按使用场景推荐

#### 🏭 生产环境部署
1. **首选**: {df.iloc[0]['Model']} - 最佳精度表现
2. **备选**: {df.iloc[1]['Model']} - 性能与效率平衡
3. **轻量**: {efficient_models.iloc[0]['Model']} - 资源友好

#### 💻 边缘计算设备
"""
        
        # 推荐轻量级模型
        lightweight_models = df[df['Parameters'].str.contains('1.0M|2.0M|3.5M', na=False)]
        for i, (_, model) in enumerate(lightweight_models.head(3).iterrows()):
            report += f"{i+1}. **{model['Model']}** (参数量: {model['Parameters']}, R²: {model['R2_Score']:.3f})\n"
        
        report += f"""

#### 🔬 研究实验
1. **Transformer**: {df[df['Type'].str.contains('Transformer', na=False)].iloc[0]['Model'] if not df[df['Type'].str.contains('Transformer', na=False)].empty else 'ViT'}
2. **现代CNN**: {df[df['Type'].str.contains('现代化CNN', na=False)].iloc[0]['Model'] if not df[df['Type'].str.contains('现代化CNN', na=False)].empty else 'ConvNeXt'}

---

## 📋 模型技术特点

"""
        
        for _, model in df.iterrows():
            config = self.model_configs.get(model['Model'], {})
            report += f"""
### {model['Model']} ({config.get('full_name', model['Model'])})

**技术特点**:
- 🏗️ **架构类型**: {model['Type']}
- 📊 **参数规模**: {model['Parameters']}
- 🎯 **R² Score**: {model['R2_Score']:.3f}
- ⚡ **综合评分**: {model['Composite_Score']:.1f}/100

**适用场景**: {'生产环境' if model['R2_Score'] > 0.75 else '研究开发' if model['R2_Score'] > 0.65 else '基线对比'}
"""
        
        report += f"""

---

## 🔍 性能分析总结

### 📊 关键发现

1. **最佳模型**: {df.iloc[0]['Model']} 在所有指标上表现最优
2. **效率之王**: {efficient_models.iloc[0]['Model']} 提供了最佳的性能/参数比
3. **稳定性**: 前3名模型的R²分数都在 {df.head(3)['R2_Score'].min():.3f} 以上
4. **模型差异**: 最佳与最差模型R²差距为 {df['R2_Score'].max() - df['R2_Score'].min():.3f}

### 💡 建议

- **生产部署**: 优先选择 {df.iloc[0]['Model']} 或 {df.iloc[1]['Model']}
- **资源受限**: 推荐使用 {efficient_models.iloc[0]['Model']}
- **实验研究**: 尝试 Transformer 类模型探索新方向
- **基线对比**: 使用 MLP 作为性能下限参考

### 🎯 性能等级分类

"""
        
        # 按R²分数分类
        excellent = df[df['R2_Score'] >= 0.75]
        good = df[(df['R2_Score'] >= 0.65) & (df['R2_Score'] < 0.75)]
        fair = df[df['R2_Score'] < 0.65]
        
        report += f"""
- **优秀级 (R² ≥ 0.75)**: {len(excellent)} 个模型
  - {', '.join(excellent['Model'].tolist())}

- **良好级 (0.65 ≤ R² < 0.75)**: {len(good)} 个模型
  - {', '.join(good['Model'].tolist()) if not good.empty else '无'}

- **基础级 (R² < 0.65)**: {len(fair)} 个模型
  - {', '.join(fair['Model'].tolist()) if not fair.empty else '无'}

---

## 📝 技术说明

### 评估指标说明
- **R² Score**: 决定系数，衡量模型解释数据方差的比例 (0-1，越大越好)
- **RMSE**: 均方根误差，预测值与真实值的标准偏差 (越小越好)
- **MAE**: 平均绝对误差，预测偏差的平均值 (越小越好)
- **MAPE**: 平均绝对百分比误差，相对误差的百分比 (越小越好)

### 测试环境
- **硬件**: GTX 1050 / RTX 3080
- **软件**: PyTorch 1.12+, Python 3.8+
- **数据**: 沈阳市122个区域的街景图像
- **配置**: batch_size=4-8, epochs=50

---

*报告生成于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*项目地址: [GitHub Repository](https://github.com/your-repo)*
"""
        
        with open(save_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"Markdown报告保存至: {save_path}")
    
    def run_complete_comparison(self):
        """运行完整的模型对比分析"""
        logger.info("开始模型对比分析...")
        
        # 加载结果
        if not self.load_results():
            logger.error("没有找到任何模型结果文件，请先训练模型")
            return
        
        # 生成可视化图表
        logger.info("生成性能对比图...")
        self.plot_performance_comparison()
        
        logger.info("生成预测散点图...")
        self.plot_prediction_scatter()
        
        # 生成报告
        logger.info("生成Markdown报告...")
        self.generate_markdown_report()
        
        # 输出简要总结
        df = self.create_performance_dataframe()
        if not df.empty:
            logger.info("\n" + "="*50)
            logger.info("🏆 模型对比总结")
            logger.info("="*50)
            logger.info(f"🥇 最佳模型: {df.iloc[0]['Model']} (R²: {df.iloc[0]['R2_Score']:.3f})")
            logger.info(f"🥈 第二名: {df.iloc[1]['Model']} (R²: {df.iloc[1]['R2_Score']:.3f})")
            logger.info(f"🥉 第三名: {df.iloc[2]['Model']} (R²: {df.iloc[2]['R2_Score']:.3f})")
            logger.info(f"📊 平均R²: {df['R2_Score'].mean():.3f}")
            logger.info(f"📈 最大R²差距: {df['R2_Score'].max() - df['R2_Score'].min():.3f}")
            logger.info("="*50)
        
        logger.info("✅ 模型对比分析完成！")
        logger.info("📁 生成的文件:")
        logger.info("   - model_performance_comparison.png: 性能对比图")
        logger.info("   - prediction_scatter_comparison.png: 预测结果散点图")
        logger.info("   - model_comparison_report.md: 详细对比报告")

def main():
    """主函数"""
    comparator = ModelComparator()
    comparator.run_complete_comparison()

if __name__ == "__main__":
    main()