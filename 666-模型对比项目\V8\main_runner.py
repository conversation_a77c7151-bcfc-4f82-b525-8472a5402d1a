"""
主运行脚本 - 优化版
用于批量训练和测试所有模型，生成完整的性能对比报告
"""

import os
import sys
import time
import logging
import argparse
import torch
import json
import pandas as pd
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'models'))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('results/training_log.txt', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def check_gpu_compatibility():
    """检查GPU兼容性"""
    if not torch.cuda.is_available():
        logger.warning("未检测到CUDA，将使用CPU训练（速度会很慢）")
        return 'cpu', 4
    
    device_name = torch.cuda.get_device_name(0)
    total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
    
    logger.info(f"检测到GPU: {device_name}")
    logger.info(f"显存: {total_memory:.1f} GB")
    
    # 为不同GPU推荐配置
    if "GTX 1050" in device_name or total_memory < 4:
        recommended_batch = 4
        logger.info("检测到入门级GPU，推荐使用小批次训练")
    elif total_memory < 8:
        recommended_batch = 8
        logger.info("检测到中等GPU，推荐中等批次训练")
    else:
        recommended_batch = 16
        logger.info("检测到高端GPU，可以使用大批次训练")
    
    return 'cuda', recommended_batch

def train_single_model(model_name, batch_size=8, epochs=50, max_images=3, save_plots=False):
    """训练单个模型"""
    logger.info(f"\n{'='*60}")
    logger.info(f"开始训练 {model_name} 模型")
    logger.info(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        # 清理GPU显存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # 动态导入和运行模型
        model_name_lower = model_name.lower()
        if model_name_lower == 'mlp':
            from models.mlp_model import main as mlp_main
            mlp_main()
        elif model_name_lower == 'mobilenet':
            from models.mobilenet_model import main as mobilenet_main
            mobilenet_main()
        elif model_name_lower == 'efficientnet':
            from models.efficientnet_model import main as efficientnet_main
            efficientnet_main()
        elif model_name_lower == 'resnet':
            from models.resnet_model import main as resnet_main
            resnet_main()
        elif model_name_lower == 'vit':
            from models.vit_model import main as vit_main
            vit_main()
        elif model_name_lower == 'convnext':
            from models.convnext_model import main as convnext_main
            convnext_main()
        elif model_name_lower == 'densenet':
            from models.densenet_model import main as densenet_main
            densenet_main()
        elif model_name_lower == 'swin':
            from models.swin_transformer_model import main as swin_main
            swin_main()
        else:
            logger.error(f"不支持的模型: {model_name}")
            return False, None
        
        end_time = time.time()
        training_time = end_time - start_time
        logger.info(f"{model_name} 训练完成，耗时: {training_time/60:.2f} 分钟")
        
        # 移动结果文件到统一目录
        move_result_files(model_name)
        
        return True, training_time
        
    except Exception as e:
        logger.error(f"{model_name} 训练失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False, None

def move_result_files(model_name):
    """移动结果文件到统一目录"""
    os.makedirs('results/test_results', exist_ok=True)
    os.makedirs('results/plots', exist_ok=True)
    os.makedirs('results/saved_models', exist_ok=True)
    
    # 移动测试结果文件
    result_files = [
        f'{model_name.lower()}_test_results.json',
        f'shenyang_{model_name}.pt',
        f'{model_name.lower()}_training_history.png',
        f'{model_name.lower()}_predictions.png',
        f'{model_name.lower()}_efficiency.png'
    ]
    
    for file in result_files:
        if os.path.exists(file):
            if file.endswith('.json'):
                target_dir = 'results/test_results'
            elif file.endswith('.pt'):
                target_dir = 'results/saved_models'
            elif file.endswith('.png'):
                target_dir = 'results/plots'
            else:
                target_dir = 'results'
            
            target_path = os.path.join(target_dir, file)
            try:
                os.rename(file, target_path)
                logger.info(f"移动文件: {file} -> {target_path}")
            except:
                pass  # 忽略移动失败

def collect_results():
    """收集所有模型的测试结果"""
    results = {}
    result_dir = 'results/test_results'
    
    if not os.path.exists(result_dir):
        logger.warning("结果目录不存在")
        return results
    
    # 查找所有结果文件
    for file in os.listdir(result_dir):
        if file.endswith('_test_results.json'):
            model_name = file.replace('_test_results.json', '').title()
            try:
                with open(os.path.join(result_dir, file), 'r') as f:
                    data = json.load(f)
                    results[model_name] = {
                        'R2_Score': data.get('r2_score', 0),
                        'RMSE': data.get('rmse', 0),
                        'MAE': data.get('mae', 0),
                        'MAPE': data.get('mape', 0)
                    }
            except Exception as e:
                logger.warning(f"读取{model_name}结果失败: {e}")
    
    return results

def create_comparison_table(results):
    """创建模型对比表格"""
    if not results:
        logger.warning("没有找到任何测试结果")
        return None
    
    # 创建DataFrame
    df = pd.DataFrame(results).T
    
    # 排序（按R2分数降序）
    df = df.sort_values('R2_Score', ascending=False)
    
    # 保存为CSV
    os.makedirs('results', exist_ok=True)
    df.to_csv('results/model_comparison.csv')
    
    # 创建Markdown表格
    markdown_table = "# 模型性能对比表\n\n"
    markdown_table += "| 模型 | R² Score | RMSE | MAE | MAPE (%) |\n"
    markdown_table += "|------|----------|------|-----|----------|\n"
    
    for model, metrics in df.iterrows():
        markdown_table += f"| {model} | {metrics['R2_Score']:.4f} | {metrics['RMSE']:.4f} | {metrics['MAE']:.4f} | {metrics['MAPE']:.2f} |\n"
    
    # 添加最佳模型说明
    best_model = df.index[0]
    markdown_table += f"\n**最佳模型**: {best_model} (R² = {df.loc[best_model, 'R2_Score']:.4f})\n"
    
    # 保存Markdown
    with open('results/model_comparison.md', 'w', encoding='utf-8') as f:
        f.write(markdown_table)
    
    return df

def create_training_summary(successful_models, failed_models, total_time):
    """创建训练总结报告"""
    summary = {
        'training_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'total_training_time_minutes': total_time / 60,
        'system_info': {
            'python_version': sys.version.split()[0],
            'pytorch_version': torch.__version__,
            'cuda_available': torch.cuda.is_available(),
            'gpu_name': torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'CPU Only'
        },
        'training_results': {
            'successful_models': successful_models,
            'failed_models': failed_models,
            'success_rate': len(successful_models) / (len(successful_models) + len(failed_models)) * 100
        }
    }
    
    # 收集测试结果
    test_results = collect_results()
    summary['model_performance'] = test_results
    
    # 保存总结
    with open('results/training_summary.json', 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    return summary

def run_all_models(models=None, batch_size=None, epochs=50, max_images=3, save_plots=False):
    """运行所有模型的训练和测试"""
    
    # 检查GPU兼容性
    device, recommended_batch = check_gpu_compatibility()
    if batch_size is None:
        batch_size = recommended_batch
    
    # 默认模型列表（按推荐顺序）
    if models is None:
        models = ['MobileNet', 'EfficientNet', 'ConvNeXt', 'DenseNet', 'ResNet', 'ViT', 'Swin', 'MLP']
    
    logger.info(f"计划训练的模型: {models}")
    logger.info(f"训练配置: batch_size={batch_size}, epochs={epochs}, max_images={max_images}")
    
    # 创建保存目录
    os.makedirs('results', exist_ok=True)
    os.makedirs('results/saved_models', exist_ok=True)
    os.makedirs('results/test_results', exist_ok=True)
    os.makedirs('results/plots', exist_ok=True)
    
    # 训练每个模型
    successful_models = []
    failed_models = []
    training_times = {}
    
    total_start_time = time.time()
    
    for i, model_name in enumerate(models, 1):
        logger.info(f"\n{'='*80}")
        logger.info(f"进度: {i}/{len(models)} - 开始训练 {model_name}")
        logger.info(f"{'='*80}")
        
        # 清理GPU显存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        success, training_time = train_single_model(
            model_name, batch_size, epochs, max_images, save_plots
        )
        
        if success:
            successful_models.append(model_name)
            training_times[model_name] = training_time
            logger.info(f"✅ {model_name} 训练成功 (耗时: {training_time/60:.1f}分钟)")
        else:
            failed_models.append(model_name)
            logger.error(f"❌ {model_name} 训练失败")
        
        # 训练间隔，让GPU休息一下
        if i < len(models):
            logger.info("等待5秒让GPU休息...")
            time.sleep(5)
    
    total_end_time = time.time()
    total_time = total_end_time - total_start_time
    
    # 收集和分析结果
    logger.info(f"\n{'='*80}")
    logger.info("收集和分析结果...")
    logger.info(f"{'='*80}")
    
    # 创建对比表格
    results = collect_results()
    comparison_df = create_comparison_table(results)
    
    # 创建训练总结
    summary = create_training_summary(successful_models, failed_models, total_time)
    
    # 打印训练总结
    logger.info(f"\n{'='*80}")
    logger.info("训练总结")
    logger.info(f"{'='*80}")
    logger.info(f"总训练时间: {total_time/3600:.2f} 小时")
    logger.info(f"成功训练的模型 ({len(successful_models)}): {successful_models}")
    if failed_models:
        logger.info(f"训练失败的模型 ({len(failed_models)}): {failed_models}")
    
    # 打印性能对比
    if comparison_df is not None:
        logger.info(f"\n{'='*80}")
        logger.info("模型性能对比 (按R²分数排序)")
        logger.info(f"{'='*80}")
        print(comparison_df.round(4))
        
        best_model = comparison_df.index[0]
        logger.info(f"\n🏆 最佳模型: {best_model}")
        logger.info(f"   R² Score: {comparison_df.loc[best_model, 'R2_Score']:.4f}")
        logger.info(f"   RMSE: {comparison_df.loc[best_model, 'RMSE']:.4f}")
        logger.info(f"   MAE: {comparison_df.loc[best_model, 'MAE']:.4f}")
    
    return successful_models, failed_models, summary, results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='街景数据预测街区能耗 - 模型训练脚本')
    
    parser.add_argument('--models', nargs='+', 
                       choices=['MLP', 'MobileNet', 'EfficientNet', 'ResNet', 'ViT', 'ConvNeXt', 'DenseNet', 'Swin'],
                       default=['MobileNet', 'EfficientNet', 'ConvNeXt', 'DenseNet', 'ResNet', 'ViT', 'Swin', 'MLP'],
                       help='要训练的模型列表')
    
    parser.add_argument('--batch_size', type=int, default=None,
                       help='批次大小 (默认根据GPU自动选择)')
    
    parser.add_argument('--epochs', type=int, default=30,
                       help='训练轮数 (默认: 30)')
    
    parser.add_argument('--max_images', type=int, default=3,
                       help='每个区域最大图像数量 (默认: 3)')
    
    parser.add_argument('--single', type=str, 
                       choices=['MLP', 'MobileNet', 'EfficientNet', 'ResNet', 'ViT', 'ConvNeXt', 'DenseNet', 'Swin'],
                       help='只训练单个模型')
    
    parser.add_argument('--all', action='store_true',
                       help='训练所有可用的模型')
    
    parser.add_argument('--compare_only', action='store_true',
                       help='只运行模型对比分析，不训练模型')
    
    parser.add_argument('--test_data_loader', action='store_true',
                       help='测试数据加载器')
    
    parser.add_argument('--save_plots', action='store_true',
                       help='保存训练图表 (可能会增加训练时间)')
    
    args = parser.parse_args()
    
    # 打印欢迎信息
    print("\n" + "="*80)
    print("🏙️  街景数据预测街区能耗 - 深度学习模型训练平台")
    print("="*80)
    print(f"📅 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 Python版本: {sys.version.split()[0]}")
    print(f"🔥 PyTorch版本: {torch.__version__}")
    print("="*80 + "\n")
    
    # 创建结果目录
    os.makedirs('results', exist_ok=True)
    
    # 测试数据加载器
    if args.test_data_loader:
        logger.info("测试数据加载器...")
        try:
            from data_loader import create_data_loaders, CONFIGS
            config = CONFIGS['shenyang']
            train_loader, val_loader, test_loader = create_data_loaders(
                config, batch_size=4, num_workers=0, max_images_per_region=2
            )
            logger.info("✅ 数据加载器测试成功")
            
            # 测试一个批次
            for batch in train_loader:
                logger.info(f"测试批次 - 图像形状: {batch['image'].shape}, 能耗形状: {batch['energy'].shape}")
                break
                
        except Exception as e:
            logger.error(f"❌ 数据加载器测试失败: {e}")
        return
    
    # 只运行对比分析
    if args.compare_only:
        logger.info("运行模型对比分析...")
        results = collect_results()
        comparison_df = create_comparison_table(results)
        if comparison_df is not None:
            print("\n模型性能对比:")
            print(comparison_df.round(4))
            logger.info("✅ 模型对比分析完成")
        else:
            logger.error("❌ 没有找到可对比的结果")
        return
    
    # 训练单个模型
    if args.single:
        logger.info(f"训练单个模型: {args.single}")
        success, training_time = train_single_model(
            args.single, args.batch_size or 8, args.epochs, args.max_images, args.save_plots
        )
        if success:
            logger.info(f"✅ {args.single} 训练完成 (耗时: {training_time/60:.1f}分钟)")
        else:
            logger.error(f"❌ {args.single} 训练失败")
        return
    
    # 设置要训练的模型
    if args.all:
        models = ['MobileNet', 'EfficientNet', 'ConvNeXt', 'DenseNet', 'ResNet', 'ViT', 'Swin', 'MLP']
    else:
        models = args.models
    
    # 训练所有模型
    successful_models, failed_models, summary, results = run_all_models(
        models=models,
        batch_size=args.batch_size,
        epochs=args.epochs,
        max_images=args.max_images,
        save_plots=args.save_plots
    )
    
    # 最终报告
    print("\n" + "="*80)
    print("🎉 训练完成！")
    print("="*80)
    print(f"✅ 成功: {len(successful_models)} 个模型")
    print(f"❌ 失败: {len(failed_models)} 个模型")
    print("\n📁 生成的文件:")
    print("   - results/training_log.txt: 详细训练日志")
    print("   - results/training_summary.json: 训练总结")
    print("   - results/model_comparison.csv: 模型对比表格")
    print("   - results/model_comparison.md: 模型对比报告")
    print("   - results/saved_models/: 训练好的模型")
    print("   - results/test_results/: 测试结果文件")
    print("   - results/plots/: 训练图表")
    print("="*80 + "\n")

if __name__ == "__main__":
    main()