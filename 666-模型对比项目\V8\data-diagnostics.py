"""
数据诊断和特征分析工具
用于深入分析街景图像数据集，找出模型性能差的原因
"""

import os
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from PIL import Image
import torch
from torchvision import transforms
from collections import Counter
import cv2
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class DataDiagnostics:
    """数据诊断工具"""
    
    def __init__(self, config_path='./data/shenyang/'):
        self.config_path = config_path
        self.region_info = None
        self.poi_mapping = None
        self.results = {}
        
    def load_data(self):
        """加载数据"""
        print("=== 加载数据 ===")
        
        # 加载区域信息
        with open(os.path.join(self.config_path, 'shenyang_region2allinfo.json'), 'r', encoding='utf-8') as f:
            self.region_info = json.load(f)
        
        # 加载POI映射
        poi_path = os.path.join(self.config_path, 'streetview_image/region_5_10_poi_image_filename.json')
        with open(poi_path, 'r', encoding='utf-8') as f:
            self.poi_mapping = json.load(f)
            
        print(f"加载了 {len(self.region_info)} 个区域的信息")
        print(f"加载了 {len(self.poi_mapping)} 个区域的POI映射")
        
    def analyze_energy_distribution(self):
        """分析能耗分布"""
        print("\n=== 能耗标签分析 ===")
        
        # 提取能耗数据
        energy_values = []
        missing_energy = []
        
        for region_id, info in self.region_info.items():
            if 'energy' in info and info['energy'] is not None:
                energy_values.append(info['energy'])
            else:
                missing_energy.append(region_id)
        
        energy_values = np.array(energy_values)
        
        # 统计信息
        print(f"有效能耗数据: {len(energy_values)} 个")
        print(f"缺失能耗数据: {len(missing_energy)} 个")
        print(f"\n能耗统计:")
        print(f"  均值: {np.mean(energy_values):.4f}")
        print(f"  标准差: {np.std(energy_values):.4f}")
        print(f"  最小值: {np.min(energy_values):.4f}")
        print(f"  最大值: {np.max(energy_values):.4f}")
        print(f"  中位数: {np.median(energy_values):.4f}")
        print(f"  偏度: {stats.skew(energy_values):.4f}")
        print(f"  峰度: {stats.kurtosis(energy_values):.4f}")
        
        # 检查异常值
        q1 = np.percentile(energy_values, 25)
        q3 = np.percentile(energy_values, 75)
        iqr = q3 - q1
        outliers = np.sum((energy_values < q1 - 1.5 * iqr) | (energy_values > q3 + 1.5 * iqr))
        print(f"  异常值数量: {outliers} ({outliers/len(energy_values)*100:.1f}%)")
        
        # 绘制分布图
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 直方图
        axes[0, 0].hist(energy_values, bins=30, edgecolor='black', alpha=0.7)
        axes[0, 0].set_title('能耗分布直方图')
        axes[0, 0].set_xlabel('能耗值')
        axes[0, 0].set_ylabel('频数')
        
        # 箱线图
        axes[0, 1].boxplot(energy_values)
        axes[0, 1].set_title('能耗分布箱线图')
        axes[0, 1].set_ylabel('能耗值')
        
        # Q-Q图
        stats.probplot(energy_values, dist="norm", plot=axes[1, 0])
        axes[1, 0].set_title('Q-Q图 (正态性检验)')
        
        # 对数变换后的分布
        log_energy = np.log1p(energy_values)  # log(1+x)避免log(0)
        axes[1, 1].hist(log_energy, bins=30, edgecolor='black', alpha=0.7, color='green')
        axes[1, 1].set_title('对数变换后的能耗分布')
        axes[1, 1].set_xlabel('log(1+能耗值)')
        axes[1, 1].set_ylabel('频数')
        
        plt.tight_layout()
        plt.savefig('energy_distribution_analysis.png', dpi=300)
        plt.show()
        
        self.results['energy_stats'] = {
            'mean': float(np.mean(energy_values)),
            'std': float(np.std(energy_values)),
            'min': float(np.min(energy_values)),
            'max': float(np.max(energy_values)),
            'median': float(np.median(energy_values)),
            'skew': float(stats.skew(energy_values)),
            'kurtosis': float(stats.kurtosis(energy_values)),
            'outliers': int(outliers)
        }
        
        # 检查是否需要数据变换
        if abs(stats.skew(energy_values)) > 1:
            print("\n⚠️ 警告: 能耗数据呈现显著偏态分布，建议进行数据变换（如对数变换）")
        
        return energy_values
    
    def analyze_image_coverage(self):
        """分析图像覆盖情况"""
        print("\n=== 图像覆盖分析 ===")
        
        # 统计每个区域的图像数量
        image_counts = []
        no_image_regions = []
        
        for region_id in self.region_info.keys():
            if region_id in self.poi_mapping:
                count = len(self.poi_mapping[region_id])
                image_counts.append(count)
            else:
                no_image_regions.append(region_id)
                image_counts.append(0)
        
        image_counts = np.array(image_counts)
        
        print(f"有图像的区域: {np.sum(image_counts > 0)} 个")
        print(f"无图像的区域: {len(no_image_regions)} 个")
        print(f"\n每个区域的图像数量统计:")
        print(f"  平均: {np.mean(image_counts):.2f}")
        print(f"  最少: {np.min(image_counts)}")
        print(f"  最多: {np.max(image_counts)}")
        
        # 图像数量分布
        count_dist = Counter(image_counts)
        print(f"\n图像数量分布:")
        for count, freq in sorted(count_dist.items()):
            print(f"  {count} 张图像: {freq} 个区域")
        
        self.results['image_coverage'] = {
            'regions_with_images': int(np.sum(image_counts > 0)),
            'regions_without_images': len(no_image_regions),
            'avg_images_per_region': float(np.mean(image_counts)),
            'max_images': int(np.max(image_counts)),
            'min_images': int(np.min(image_counts))
        }
    
    def analyze_image_quality(self, sample_size=100):
        """分析图像质量"""
        print(f"\n=== 图像质量分析 (采样{sample_size}张) ===")
        
        image_dir = os.path.join(self.config_path, 'streetview_image/Region')
        
        # 随机采样图像
        all_images = []
        for region_id, filenames in self.poi_mapping.items():
            for filename in filenames:
                image_path = os.path.join(image_dir, region_id, filename)
                if os.path.exists(image_path):
                    all_images.append(image_path)
        
        if len(all_images) > sample_size:
            sampled_images = np.random.choice(all_images, sample_size, replace=False)
        else:
            sampled_images = all_images
        
        # 分析图像属性
        sizes = []
        brightness_values = []
        contrast_values = []
        sharpness_values = []
        
        for img_path in sampled_images:
            try:
                # 打开图像
                img = Image.open(img_path).convert('RGB')
                sizes.append(img.size)
                
                # 转换为numpy数组
                img_array = np.array(img)
                
                # 计算亮度
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
                brightness = np.mean(gray)
                brightness_values.append(brightness)
                
                # 计算对比度
                contrast = np.std(gray)
                contrast_values.append(contrast)
                
                # 计算清晰度（拉普拉斯方差）
                laplacian = cv2.Laplacian(gray, cv2.CV_64F)
                sharpness = laplacian.var()
                sharpness_values.append(sharpness)
                
            except Exception as e:
                print(f"处理图像时出错 {img_path}: {e}")
        
        # 统计结果
        unique_sizes = Counter(sizes)
        print(f"\n图像尺寸分布:")
        for size, count in unique_sizes.most_common():
            print(f"  {size}: {count} 张")
        
        print(f"\n图像质量指标:")
        print(f"  平均亮度: {np.mean(brightness_values):.2f} (范围: 0-255)")
        print(f"  平均对比度: {np.mean(contrast_values):.2f}")
        print(f"  平均清晰度: {np.mean(sharpness_values):.2f}")
        
        # 检查问题
        dark_images = np.sum(np.array(brightness_values) < 50)
        bright_images = np.sum(np.array(brightness_values) > 200)
        low_contrast = np.sum(np.array(contrast_values) < 30)
        blurry = np.sum(np.array(sharpness_values) < 100)
        
        if dark_images > sample_size * 0.1:
            print(f"\n⚠️ 警告: {dark_images} 张图像过暗")
        if bright_images > sample_size * 0.1:
            print(f"⚠️ 警告: {bright_images} 张图像过亮")
        if low_contrast > sample_size * 0.2:
            print(f"⚠️ 警告: {low_contrast} 张图像对比度过低")
        if blurry > sample_size * 0.2:
            print(f"⚠️ 警告: {blurry} 张图像可能模糊")
        
        self.results['image_quality'] = {
            'avg_brightness': float(np.mean(brightness_values)),
            'avg_contrast': float(np.mean(contrast_values)),
            'avg_sharpness': float(np.mean(sharpness_values)),
            'dark_images': int(dark_images),
            'bright_images': int(bright_images),
            'low_contrast_images': int(low_contrast),
            'blurry_images': int(blurry)
        }
    
    def analyze_data_splits(self):
        """分析数据集划分"""
        print("\n=== 数据集划分分析 ===")
        
        # 加载划分文件
        splits = {}
        for split in ['train', 'valid', 'test']:
            file_path = os.path.join(self.config_path, f'shenyang_zl15_{split}.csv')
            if os.path.exists(file_path):
                df = pd.read_csv(file_path)
                splits[split] = df['BlockID'].tolist()
                print(f"{split}集: {len(df)} 个样本")
        
        # 检查能耗分布是否平衡
        print("\n各数据集的能耗分布:")
        for split_name, region_ids in splits.items():
            energies = []
            for region_id in region_ids:
                if str(region_id) in self.region_info:
                    energy = self.region_info[str(region_id)].get('energy')
                    if energy is not None:
                        energies.append(energy)
            
            if energies:
                print(f"\n{split_name}集:")
                print(f"  样本数: {len(energies)}")
                print(f"  均值: {np.mean(energies):.4f}")
                print(f"  标准差: {np.std(energies):.4f}")
                print(f"  范围: [{np.min(energies):.4f}, {np.max(energies):.4f}]")
    
    def analyze_correlation(self):
        """分析特征相关性"""
        print("\n=== 特征相关性分析 ===")
        
        # 收集可用的特征
        features = []
        labels = []
        
        for region_id, info in self.region_info.items():
            if 'energy' in info and info['energy'] is not None:
                feature_dict = {
                    'energy': info['energy'],
                    'num_images': len(self.poi_mapping.get(region_id, [])),
                    'has_images': 1 if region_id in self.poi_mapping else 0
                }
                
                # 添加其他可能的特征
                for key in ['population', 'area', 'density', 'gdp']:
                    if key in info:
                        feature_dict[key] = info[key]
                
                features.append(feature_dict)
        
        if features:
            df = pd.DataFrame(features)
            
            # 计算相关性
            if len(df.columns) > 1:
                corr_matrix = df.corr()
                
                # 绘制热力图
                plt.figure(figsize=(10, 8))
                sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0,
                           square=True, linewidths=0.5)
                plt.title('特征相关性热力图')
                plt.tight_layout()
                plt.savefig('feature_correlation_heatmap.png', dpi=300)
                plt.show()
                
                # 找出与能耗相关性最强的特征
                energy_corr = corr_matrix['energy'].drop('energy').abs().sort_values(ascending=False)
                print("\n与能耗相关性最强的特征:")
                for feature, corr in energy_corr.items():
                    print(f"  {feature}: {corr:.4f}")
    
    def generate_report(self):
        """生成诊断报告"""
        print("\n=== 生成诊断报告 ===")
        
        report = {
            'diagnosis_time': pd.Timestamp.now().isoformat(),
            'results': self.results,
            'recommendations': []
        }
        
        # 基于分析结果提供建议
        if 'energy_stats' in self.results:
            if self.results['energy_stats']['skew'] > 1:
                report['recommendations'].append("能耗数据呈右偏分布，建议使用对数变换")
            if self.results['energy_stats']['outliers'] > 10:
                report['recommendations'].append("存在较多异常值，建议进行异常值处理")
        
        if 'image_coverage' in self.results:
            if self.results['image_coverage']['regions_without_images'] > 0:
                report['recommendations'].append("部分区域缺少图像数据，需要处理缺失值")
        
        if 'image_quality' in self.results:
            if self.results['image_quality']['avg_brightness'] < 100:
                report['recommendations'].append("图像整体偏暗，可能需要增强预处理")
        
        # 保存报告
        with open('data_diagnosis_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print("\n诊断报告已保存至: data_diagnosis_report.json")
        print("\n主要建议:")
        for i, rec in enumerate(report['recommendations'], 1):
            print(f"{i}. {rec}")
    
    def run_full_diagnosis(self):
        """运行完整诊断"""
        self.load_data()
        self.analyze_energy_distribution()
        self.analyze_image_coverage()
        self.analyze_image_quality()
        self.analyze_data_splits()
        self.analyze_correlation()
        self.generate_report()


def check_model_predictions(result_files):
    """检查模型预测结果"""
    print("\n=== 模型预测结果诊断 ===")
    
    for model_name, file_path in result_files.items():
        if os.path.exists(file_path):
            print(f"\n{model_name} 模型:")
            
            with open(file_path, 'r') as f:
                results = json.load(f)
            
            predictions = np.array(results['predictions'])
            targets = np.array(results['targets'])
            
            # 检查预测范围
            print(f"  预测范围: [{predictions.min():.4f}, {predictions.max():.4f}]")
            print(f"  目标范围: [{targets.min():.4f}, {targets.max():.4f}]")
            
            # 检查预测分布
            pred_std = predictions.std()
            target_std = targets.std()
            print(f"  预测标准差: {pred_std:.4f}")
            print(f"  目标标准差: {target_std:.4f}")
            
            # 检查是否退化为常数预测
            if pred_std < target_std * 0.1:
                print(f"  ⚠️ 警告: 模型可能退化为近似常数预测！")
            
            # 检查预测偏差
            bias = (predictions - targets).mean()
            print(f"  平均偏差: {bias:.4f}")
            
            # 绘制预测vs实际散点图
            plt.figure(figsize=(8, 6))
            plt.scatter(targets, predictions, alpha=0.5)
            plt.plot([targets.min(), targets.max()], [targets.min(), targets.max()], 'r--', lw=2)
            plt.xlabel('实际值')
            plt.ylabel('预测值')
            plt.title(f'{model_name} 预测结果')
            plt.savefig(f'{model_name.lower()}_prediction_scatter.png', dpi=300)
            plt.close()


if __name__ == "__main__":
    # 运行数据诊断
    diagnostics = DataDiagnostics()
    diagnostics.run_full_diagnosis()
    
    # 检查模型预测结果
    result_files = {
        'MobileNet': './results/mobilenet_test_results.json',
        'EfficientNet': './results/efficientnet_test_results.json',
        'ResNet': './results/resnet_test_results.json',
        'ViT': './results/vit_test_results.json'
    }
    
    check_model_predictions(result_files)
    
    print("\n诊断完成！请查看生成的报告和图表。")
