"""
环境验证和快速测试脚本
用于验证项目环境是否正确配置，进行快速功能测试
"""

import sys
import os
import platform
import subprocess
import importlib
import torch
import json
from pathlib import Path

def print_section(title):
    """打印节标题"""
    print("\n" + "="*60)
    print(f"🔍 {title}")
    print("="*60)

def print_result(test_name, success, message=""):
    """打印测试结果"""
    status = "✅" if success else "❌"
    print(f"{status} {test_name}: {message}")

def check_python_version():
    """检查Python版本"""
    print_section("Python环境检查")
    
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    print(f"平台: {platform.platform()}")
    print(f"架构: {platform.architecture()[0]}")
    
    # 检查版本兼容性
    if version.major == 3 and version.minor >= 7:
        print_result("Python版本", True, f"兼容 (>= 3.7)")
        return True
    else:
        print_result("Python版本", False, f"需要Python 3.7+")
        return False

def check_pytorch():
    """检查PyTorch安装"""
    print_section("PyTorch环境检查")
    
    try:
        import torch
        import torchvision
        
        print(f"PyTorch版本: {torch.__version__}")
        print(f"torchvision版本: {torchvision.__version__}")
        
        # 检查CUDA
        cuda_available = torch.cuda.is_available()
        print(f"CUDA可用: {cuda_available}")
        
        if cuda_available:
            print(f"CUDA版本: {torch.version.cuda}")
            print(f"GPU数量: {torch.cuda.device_count()}")
            
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
        
        print_result("PyTorch安装", True)
        return True
        
    except ImportError as e:
        print_result("PyTorch安装", False, f"导入失败: {e}")
        return False

def check_dependencies():
    """检查依赖包"""
    print_section("依赖包检查")
    
    required_packages = [
        'numpy', 'pandas', 'scikit-learn', 'matplotlib', 
        'seaborn', 'PIL', 'tqdm', 'cv2'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
                version = PIL.__version__
            elif package == 'cv2':
                import cv2
                version = cv2.__version__
            else:
                module = importlib.import_module(package)
                version = getattr(module, '__version__', 'unknown')
            
            print_result(f"{package}", True, f"v{version}")
            
        except ImportError:
            print_result(f"{package}", False, "未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺失的包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True

def check_data_structure():
    """检查数据结构"""
    print_section("数据结构检查")
    
    # 从data_loader导入配置
    try:
        from data_loader import CONFIGS
        config = CONFIGS['shenyang']
        
        required_files = [
            config['region2allinfo_path'],
            config['train_idx_path'],
            config['val_idx_path'],
            config['test_idx_path'],
            config['poi_streetview_filename_path']
        ]
        
        required_dirs = [
            config['streetview_image_dir']
        ]
        
        all_exists = True
        
        # 检查文件
        for file_path in required_files:
            exists = os.path.exists(file_path)
            print_result(f"文件: {os.path.basename(file_path)}", exists, file_path)
            all_exists &= exists
        
        # 检查目录
        for dir_path in required_dirs:
            exists = os.path.exists(dir_path)
            print_result(f"目录: {os.path.basename(dir_path)}", exists, dir_path)
            all_exists &= exists
        
        return all_exists
        
    except Exception as e:
        print_result("数据配置", False, f"配置加载失败: {e}")
        return False

def test_data_loader():
    """测试数据加载器"""
    print_section("数据加载器测试")
    
    try:
        from data_loader import create_data_loaders, CONFIGS
        
        config = CONFIGS['shenyang']
        
        # 创建小批次测试
        train_loader, val_loader, test_loader = create_data_loaders(
            config, 
            batch_size=2, 
            num_workers=0, 
            max_images_per_region=1
        )
        
        print(f"训练集大小: {len(train_loader.dataset)}")
        print(f"验证集大小: {len(val_loader.dataset)}")
        print(f"测试集大小: {len(test_loader.dataset)}")
        
        # 测试一个批次
        try:
            batch = next(iter(train_loader))
            print(f"批次图像形状: {batch['image'].shape}")
            print(f"批次能耗形状: {batch['energy'].shape}")
            print_result("数据加载测试", True)
            return True
        except Exception as e:
            print_result("数据加载测试", False, f"批次加载失败: {e}")
            return False
            
    except Exception as e:
        print_result("数据加载器测试", False, f"导入失败: {e}")
        return False

def test_model_import():
    """测试模型导入"""
    print_section("模型导入测试")
    
    models = {
        'MLP': 'models.mlp_model',
        'MobileNet': 'models.mobilenet_model',
        'EfficientNet': 'models.efficientnet_model',
        'ResNet': 'models.resnet_model',
        'ViT': 'models.vit_model'
    }
    
    success_count = 0
    
    for model_name, module_name in models.items():
        try:
            importlib.import_module(module_name)
            print_result(f"{model_name}模型", True)
            success_count += 1
        except ImportError as e:
            print_result(f"{model_name}模型", False, f"导入失败: {e}")
    
    return success_count == len(models)

def run_quick_training_test():
    """运行快速训练测试"""
    print_section("快速训练测试")
    
    print("⚠️  这将进行一个简短的模型训练测试 (1个epoch)")
    response = input("是否继续? (y/N): ").lower().strip()
    
    if response != 'y':
        print("跳过训练测试")
        return True
    
    try:
        # 设置环境变量以加速测试
        os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
        
        # 导入并运行快速测试
        from models.mobilenet_model import MobileNetFeatureExtractor, MobileNetTrainer
        from data_loader import create_data_loaders, CONFIGS
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {device}")
        
        # 加载数据
        config = CONFIGS['shenyang']
        train_loader, val_loader, _ = create_data_loaders(
            config, 
            batch_size=2, 
            num_workers=0, 
            max_images_per_region=1
        )
        
        # 创建模型
        model = MobileNetFeatureExtractor(output_dim=64, dropout=0.1)
        trainer = MobileNetTrainer(model, device=device)
        
        # 训练1个epoch
        print("开始快速训练测试...")
        train_loss, train_r2 = trainer.train_epoch(train_loader)
        val_loss, val_r2, _, _, _, _ = trainer.validate(val_loader)
        
        print(f"训练损失: {train_loss:.4f}, R²: {train_r2:.4f}")
        print(f"验证损失: {val_loss:.4f}, R²: {val_r2:.4f}")
        
        print_result("快速训练测试", True)
        return True
        
    except Exception as e:
        print_result("快速训练测试", False, f"训练失败: {e}")
        return False

def create_test_report(results):
    """创建测试报告"""
    print_section("测试报告")
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"通过率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过! 环境配置正确，可以开始训练模型。")
        print("\n建议的下一步:")
        print("1. 运行快速模型训练: python main_runner.py --single MobileNet --epochs 5")
        print("2. 运行完整训练: python main_runner.py")
        print("3. 查看结果对比: python model_comparator.py")
    else:
        print("\n❌ 存在问题，请根据上述检查结果修复环境。")
        print("\n常见解决方案:")
        print("1. 安装缺失的依赖: pip install -r requirements.txt")
        print("2. 检查数据路径配置")
        print("3. 确保CUDA驱动正确安装")
    
    # 保存报告
    report = {
        'timestamp': torch.cuda.Event().record() if torch.cuda.is_available() else 'cpu_time',
        'system_info': {
            'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            'platform': platform.platform(),
            'pytorch_version': torch.__version__ if 'torch' in globals() else 'Not installed',
            'cuda_available': torch.cuda.is_available() if 'torch' in globals() else False
        },
        'test_results': results,
        'total_tests': total_tests,
        'passed_tests': passed_tests,
        'pass_rate': passed_tests/total_tests*100
    }
    
    with open('setup_test_report.json', 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    print(f"\n📄 详细报告已保存至: setup_test_report.json")

def main():
    """主函数"""
    print("🚀 街景数据预测街区能耗项目 - 环境验证工具")
    print("=" * 60)
    
    # 运行所有测试
    results = {}
    
    results['python_version'] = check_python_version()
    results['pytorch_install'] = check_pytorch()
    results['dependencies'] = check_dependencies()
    results['data_structure'] = check_data_structure()
    results['data_loader'] = test_data_loader()
    results['model_import'] = test_model_import()
    
    # 可选的训练测试
    print_section("可选测试")
    run_training = input("是否运行快速训练测试? (y/N): ").lower().strip() == 'y'
    if run_training:
        results['quick_training'] = run_quick_training_test()
    
    # 生成报告
    create_test_report(results)

if __name__ == "__main__":
    main()
