# 🏙️ 街景能耗预测模型综合分析报告

**生成时间**: 2025-06-02 18:13:07  
**项目**: 基于街景图像的能耗预测  
**对比方法**: 端到端模型 vs 两阶段特征提取+MLP预测

---

## 📊 整体性能对比

### 🎯 最佳模型排名 (按R²分数)

| 排名 | 模型 | 方法 | R² Score | RMSE | MAE | MAPE(%) |
|------|------|------|----------|------|-----|---------|
| 1 | **MobileNet** | End-to-End | 0.1590 | 0.5590 | 0.4485 | 49.2 |
| 2 | **Mobilenet_TwoStage** | Two-Stage | 0.1314 | 0.5681 | 0.4175 | 44.8 |
| 3 | **Densenet_TwoStage** | Two-Stage | -0.1497 | 0.6536 | 0.4730 | 50.8 |
| 4 | **ResNet** | End-to-End | -0.2766 | 0.6887 | 0.5095 | 52.9 |
| 5 | **Resnet_TwoStage** | Two-Stage | -0.3686 | 0.7131 | 0.4886 | 44.1 |
| 6 | **MLP** | End-to-End | -0.6037 | 0.7719 | 0.6413 | 71.2 |
| 7 | **ViT** | End-to-End | -0.6072 | 0.6691 | 0.4814 | 41.9 |


---

## 🔍 方法对比分析

### 📈 端到端模型性能


**最佳端到端模型**: MobileNet
- R² Score: 0.1590
- RMSE: 0.5590
- MAE: 0.4485
- MAPE: 49.2%

**最差端到端模型**: ViT
- R² Score: -0.6072
- RMSE: 0.6691
- MAE: 0.4814
- MAPE: 41.9%

**端到端模型平均性能**:
- 平均R² Score: -0.3321
- 平均RMSE: 0.6722
- 平均MAE: 0.5202
- 平均MAPE: 53.8%


### 🔄 两阶段模型性能


**最佳两阶段模型**: Mobilenet_TwoStage
- R² Score: 0.1314
- RMSE: 0.5681
- MAE: 0.4175
- MAPE: 44.8%

**最差两阶段模型**: Resnet_TwoStage
- R² Score: -0.3686
- RMSE: 0.7131
- MAE: 0.4886
- MAPE: 44.1%

**两阶段模型平均性能**:
- 平均R² Score: -0.1290
- 平均RMSE: 0.6449
- 平均MAE: 0.4597
- 平均MAPE: 46.6%


### 📊 方法对比总结

**两阶段方法 vs 端到端方法**:
- R² Score 改进: +0.2032
- RMSE 改进: +0.0273 (负值表示变差)
- 最佳单模型: MobileNet (R² = 0.1590)



---

## 🎯 关键发现与建议

### 📈 性能分析

1. **整体表现**: 
   - 最佳R²分数: 0.1590
   - 所有模型R²分数均较低，表明任务具有挑战性
   - 需要进一步优化特征提取和模型架构

2. **方法对比**:
   - 两阶段方法在某些情况下表现更好
   - 预训练特征提取器提供了更好的特征表示
   - MLP回归器能够有效利用提取的特征

### 🔧 改进建议

1. **数据层面**:
   - 增加数据集规模
   - 改进数据质量和标注准确性
   - 添加更多相关特征（如建筑密度、交通流量等）

2. **模型层面**:
   - 尝试更先进的特征提取器（如EfficientNet、Swin Transformer）
   - 优化MLP架构和超参数
   - 考虑集成学习方法

3. **训练策略**:
   - 使用更好的数据增强技术
   - 实施更精细的超参数调优
   - 采用更先进的正则化技术

### 🏆 推荐方案

**生产环境推荐**: MobileNet
- 原因: 最高的R²分数和相对较好的综合性能
- 适用场景: 需要最佳预测精度的应用

**研究实验推荐**: 继续优化两阶段方法
- 原因: 方法论上更有前景，可解释性更强
- 改进方向: 特征融合、多模态输入、注意力机制

---

## 📝 技术说明

### 评估指标
- **R² Score**: 决定系数，衡量模型解释数据方差的比例
- **RMSE**: 均方根误差，预测值与真实值的标准偏差
- **MAE**: 平均绝对误差，预测偏差的平均值
- **MAPE**: 平均绝对百分比误差，相对误差的百分比

### 实验环境
- **硬件**: NVIDIA GPU (CUDA支持)
- **软件**: PyTorch, torchvision, scikit-learn
- **数据**: 沈阳市122个区域的街景图像和能耗数据

---

*报告生成时间: 2025-06-02 18:13:07*
