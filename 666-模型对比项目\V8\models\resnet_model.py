"""
ResNet模型 - 优化版本
论文: Deep Residual Learning for Image Recognition
链接: https://arxiv.org/abs/1512.03385

针对街区能耗预测任务优化，解决小批次训练和性能问题
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import os
import json
from datetime import datetime
import logging
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import matplotlib.pyplot as plt
from torchvision import models

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImprovedResNetFeatureExtractor(nn.Module):
    """改进的ResNet特征提取器"""
    
    def __init__(self, model_name='resnet50', output_dim=128, dropout=0.3, pretrained=True):
        super(ImprovedResNetFeatureExtractor, self).__init__()
        
        # 加载预训练的ResNet
        if model_name == 'resnet18':
            self.backbone = models.resnet18(pretrained=pretrained)
            feature_dim = 512
        elif model_name == 'resnet34':
            self.backbone = models.resnet34(pretrained=pretrained)
            feature_dim = 512
        elif model_name == 'resnet50':
            self.backbone = models.resnet50(pretrained=pretrained)
            feature_dim = 2048
        else:
            raise ValueError(f"不支持的ResNet模型: {model_name}")
        
        # 移除原始的全连接层
        self.backbone.fc = nn.Identity()
        
        # 冻结早期层以提高训练稳定性
        self._freeze_early_layers()
        
        # 使用GroupNorm替代BatchNorm，解决小批次问题
        self.feature_head = nn.Sequential(
            nn.Dropout(dropout),
            nn.Linear(feature_dim, 1024),
            nn.GroupNorm(32, 1024),  # 使用GroupNorm
            nn.ReLU(inplace=True),
            nn.Dropout(dropout / 2),
            nn.Linear(1024, 512),
            nn.GroupNorm(16, 512),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout / 2),
            nn.Linear(512, output_dim),
            nn.ReLU(inplace=True)
        )
        
        # 回归头
        self.regression_head = nn.Sequential(
            nn.Dropout(dropout / 2),
            nn.Linear(output_dim, 64),
            nn.GroupNorm(4, 64),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout / 4),
            nn.Linear(64, 32),
            nn.ReLU(inplace=True),
            nn.Linear(32, 1)
        )
        
        self._initialize_weights()
    
    def _freeze_early_layers(self):
        """冻结早期层以提高训练稳定性"""
        # 冻结conv1, bn1, layer1
        for param in self.backbone.conv1.parameters():
            param.requires_grad = False
        for param in self.backbone.bn1.parameters():
            param.requires_grad = False
        for param in self.backbone.layer1.parameters():
            param.requires_grad = False
        
        logger.info("已冻结ResNet早期层")
    
    def _initialize_weights(self):
        """初始化新添加的层"""
        for m in [self.feature_head, self.regression_head]:
            for layer in m:
                if isinstance(layer, nn.Linear):
                    nn.init.xavier_uniform_(layer.weight)
                    nn.init.constant_(layer.bias, 0)
                elif isinstance(layer, nn.GroupNorm):
                    nn.init.constant_(layer.weight, 1)
                    nn.init.constant_(layer.bias, 0)
    
    def forward(self, x):
        # 特征提取
        features = self.backbone(x)
        
        # 生成嵌入
        embeddings = self.feature_head(features)
        
        # 回归预测
        output = self.regression_head(embeddings)
        output = output.view(-1)
        
        return output, embeddings
    
    def unfreeze_all_layers(self):
        """解冻所有层进行微调"""
        for param in self.backbone.parameters():
            param.requires_grad = True
        logger.info("已解冻所有层")

class ImprovedResNetTrainer:
    """改进的ResNet训练器"""
    
    def __init__(self, model, device='cuda', learning_rate=0.0001, weight_decay=1e-4):
        self.model = model.to(device)
        self.device = device
        self.criterion = nn.SmoothL1Loss()  # 使用SmoothL1Loss替代MSE
        
        # 分层学习率：backbone使用较小学习率，新层使用较大学习率
        backbone_params = []
        new_layer_params = []
        
        for name, param in model.named_parameters():
            if param.requires_grad:
                if 'backbone' in name:
                    backbone_params.append(param)
                else:
                    new_layer_params.append(param)
        
        # 使用SGD优化器，对ResNet更有效
        self.optimizer = optim.SGD([
            {'params': backbone_params, 'lr': learning_rate * 0.1, 'weight_decay': weight_decay},
            {'params': new_layer_params, 'lr': learning_rate, 'weight_decay': weight_decay}
        ], momentum=0.9, nesterov=True)
        
        # 使用MultiStepLR调度器
        self.scheduler = optim.lr_scheduler.MultiStepLR(
            self.optimizer, 
            milestones=[30, 45], 
            gamma=0.1
        )
        
        self.train_losses = []
        self.val_losses = []
        self.train_r2_scores = []
        self.val_r2_scores = []
        self.learning_rates = []
        
        # 最佳模型状态
        self.best_val_loss = float('inf')
        self.patience_counter = 0
        self.max_patience = 20
    
    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        predictions = []
        targets = []
        batch_count = 0
        
        for batch_idx, batch in enumerate(train_loader):
            images = batch['image'].to(self.device)
            energy = batch['energy'].to(self.device)
            
            self.optimizer.zero_grad()
            outputs, _ = self.model(images)
            loss = self.criterion(outputs, energy)
            
            # 检查loss是否有效
            if torch.isnan(loss) or torch.isinf(loss):
                logger.warning(f"检测到异常loss: {loss.item()}, 跳过该批次")
                continue
            
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            total_loss += loss.item()
            batch_count += 1
            
            # 安全地收集预测结果
            pred_np = outputs.detach().cpu().numpy()
            target_np = energy.detach().cpu().numpy()
            
            if pred_np.ndim == 0:
                pred_np = [pred_np.item()]
            if target_np.ndim == 0:
                target_np = [target_np.item()]
            
            predictions.extend(pred_np.tolist())
            targets.extend(target_np.tolist())
            
            if batch_idx % 10 == 0:
                logger.info(f'训练批次 {batch_idx}/{len(train_loader)}, 损失: {loss.item():.4f}')
        
        if batch_count == 0:
            return 0, 0
        
        avg_loss = total_loss / batch_count
        
        # 安全地计算R²
        try:
            if len(set(targets)) > 1 and len(predictions) > 1:
                r2 = r2_score(targets, predictions)
            else:
                r2 = 0.0
        except Exception as e:
            logger.warning(f"R²计算失败: {e}")
            r2 = 0.0
        
        return avg_loss, r2
    
    def validate(self, val_loader):
        """验证模型"""
        self.model.eval()
        total_loss = 0
        predictions = []
        targets = []
        batch_count = 0
        
        with torch.no_grad():
            for batch in val_loader:
                images = batch['image'].to(self.device)
                energy = batch['energy'].to(self.device)
                
                outputs, _ = self.model(images)
                loss = self.criterion(outputs, energy)
                
                if not (torch.isnan(loss) or torch.isinf(loss)):
                    total_loss += loss.item()
                    batch_count += 1
                    
                    # 安全地收集预测结果
                    pred_np = outputs.cpu().numpy()
                    target_np = energy.cpu().numpy()
                    
                    if pred_np.ndim == 0:
                        pred_np = [pred_np.item()]
                    if target_np.ndim == 0:
                        target_np = [target_np.item()]
                    
                    predictions.extend(pred_np.tolist())
                    targets.extend(target_np.tolist())
        
        if batch_count == 0:
            return float('inf'), 0, float('inf'), float('inf'), [], []
        
        avg_loss = total_loss / batch_count
        
        # 安全地计算指标
        try:
            if len(set(targets)) > 1 and len(predictions) > 1:
                r2 = r2_score(targets, predictions)
                rmse = np.sqrt(mean_squared_error(targets, predictions))
                mae = mean_absolute_error(targets, predictions)
            else:
                r2 = 0.0
                rmse = float('inf')
                mae = float('inf')
        except Exception as e:
            logger.warning(f"指标计算失败: {e}")
            r2 = 0.0
            rmse = float('inf')
            mae = float('inf')
        
        return avg_loss, r2, rmse, mae, predictions, targets
    
    def train(self, train_loader, val_loader, epochs=50, save_dir='./results/saved_models', 
              warmup_epochs=5):
        """训练模型"""
        os.makedirs(save_dir, exist_ok=True)
        best_model_path = None
        
        logger.info(f"开始训练ResNet模型，总共 {epochs} 个epoch")
        logger.info(f"预热期: {warmup_epochs} epochs")
        
        for epoch in range(epochs):
            # 记录学习率
            current_lr = self.optimizer.param_groups[0]['lr']
            self.learning_rates.append(current_lr)
            
            # Warmup阶段：解冻更多层
            if epoch == warmup_epochs:
                self.model.unfreeze_all_layers()
                logger.info(f"Epoch {epoch}: 解冻所有层进行微调")
                
                # 更新优化器以包含新解冻的参数
                backbone_params = []
                new_layer_params = []
                
                for name, param in self.model.named_parameters():
                    if param.requires_grad:
                        if 'backbone' in name:
                            backbone_params.append(param)
                        else:
                            new_layer_params.append(param)
                
                self.optimizer = optim.SGD([
                    {'params': backbone_params, 'lr': current_lr * 0.01, 'weight_decay': 1e-4},
                    {'params': new_layer_params, 'lr': current_lr, 'weight_decay': 1e-4}
                ], momentum=0.9, nesterov=True)
            
            # 训练
            train_loss, train_r2 = self.train_epoch(train_loader)
            
            # 验证
            val_loss, val_r2, val_rmse, val_mae, _, _ = self.validate(val_loader)
            
            # 学习率调度
            self.scheduler.step()
            
            # 记录历史
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.train_r2_scores.append(train_r2)
            self.val_r2_scores.append(val_r2)
            
            logger.info(f'Epoch {epoch+1}/{epochs}:')
            logger.info(f'  学习率: {current_lr:.6f}')
            logger.info(f'  训练损失: {train_loss:.4f}, 训练R²: {train_r2:.4f}')
            logger.info(f'  验证损失: {val_loss:.4f}, 验证R²: {val_r2:.4f}')
            logger.info(f'  验证RMSE: {val_rmse:.4f}, 验证MAE: {val_mae:.4f}')
            
            # 保存最佳模型
            if val_loss < self.best_val_loss and val_loss != float('inf'):
                self.best_val_loss = val_loss
                self.patience_counter = 0
                best_model_path = os.path.join(save_dir, 'shenyang_ResNet_improved.pt')
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'val_loss': val_loss,
                    'val_r2': val_r2,
                    'config': {
                        'model_type': 'ResNet-Improved',
                        'model_name': 'resnet50',
                        'output_dim': 128,
                        'dropout': 0.3
                    }
                }, best_model_path)
                logger.info(f'保存最佳模型: {best_model_path}')
            else:
                self.patience_counter += 1
                if self.patience_counter >= self.max_patience:
                    logger.info(f'早停: 验证损失在 {self.max_patience} 个epoch内未改善')
                    break
        
        logger.info(f"训练完成! 最佳验证损失: {self.best_val_loss:.4f}")
        return best_model_path
    
    def plot_training_history(self, save_path='resnet_improved_training_history.png'):
        """绘制训练历史"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # 损失曲线
        ax1.plot(self.train_losses, label='训练损失', color='blue', linewidth=2)
        ax1.plot(self.val_losses, label='验证损失', color='red', linewidth=2)
        ax1.set_title('ResNet-50 (改进版) 训练和验证损失', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('SmoothL1 Loss')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # R²分数
        ax2.plot(self.train_r2_scores, label='训练R²', color='blue', linewidth=2)
        ax2.plot(self.val_r2_scores, label='验证R²', color='red', linewidth=2)
        ax2.set_title('ResNet-50 R²分数变化', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('R² Score')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 学习率变化
        ax3.plot(self.learning_rates, color='green', linewidth=2)
        ax3.set_title('学习率变化', fontsize=14, fontweight='bold')
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('Learning Rate')
        ax3.set_yscale('log')
        ax3.grid(True, alpha=0.3)
        
        # 改进信息
        info_text = f"""ResNet-50 改进版信息:
        
• 损失函数: SmoothL1Loss
• 优化器: SGD + Nesterov
• 学习率调度: MultiStepLR
• 归一化: GroupNorm (解决小批次问题)
• 分层学习率: backbone较小，新层较大
• 层冻结策略: 前5轮冻结早期层
• 梯度裁剪: max_norm=1.0
• 总训练轮数: {len(self.train_losses)}
• 最佳验证R²: {max(self.val_r2_scores) if self.val_r2_scores else 0:.4f}
• 最低验证损失: {min(self.val_losses) if self.val_losses else float('inf'):.4f}

主要改进:
• 🔧 GroupNorm替代BatchNorm
• 📚 分层学习率和冻结策略  
• 💪 SmoothL1Loss提高鲁棒性
• ⚡ SGD优化器提升收敛
        """
        ax4.text(0.05, 0.95, info_text, transform=ax4.transAxes, 
                verticalalignment='top', fontsize=9,
                bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
        ax4.set_title('模型改进信息', fontsize=14, fontweight='bold')
        ax4.axis('off')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        logger.info(f"ResNet改进版训练历史图保存至: {save_path}")

def test_model(model_path, test_loader, device='cuda'):
    """测试改进的ResNet模型"""
    checkpoint = torch.load(model_path, map_location=device)
    config = checkpoint['config']
    
    model = ImprovedResNetFeatureExtractor(
        model_name=config['model_name'],
        output_dim=config['output_dim'],
        dropout=0.0  # 测试时不使用dropout
    ).to(device)
    
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    predictions = []
    targets = []
    region_ids = []
    
    with torch.no_grad():
        for batch in test_loader:
            images = batch['image'].to(device)
            energy = batch['energy'].to(device)
            batch_region_ids = batch['region_id']
            
            outputs, _ = model(images)
            
            predictions.extend(outputs.cpu().numpy())
            targets.extend(energy.cpu().numpy())
            
            if torch.is_tensor(batch_region_ids):
                region_ids.extend(batch_region_ids.cpu().numpy())
            else:
                region_ids.extend(batch_region_ids)
    
    # 计算指标
    r2 = r2_score(targets, predictions)
    rmse = np.sqrt(mean_squared_error(targets, predictions))
    mae = mean_absolute_error(targets, predictions)
    
    # 安全计算MAPE
    targets_array = np.array(targets)
    predictions_array = np.array(predictions)
    non_zero_mask = targets_array != 0
    if np.any(non_zero_mask):
        mape = np.mean(np.abs((targets_array[non_zero_mask] - predictions_array[non_zero_mask]) / targets_array[non_zero_mask])) * 100
    else:
        mape = float('inf')
    
    results = {
        'model_type': 'ResNet-Improved',
        'r2_score': r2,
        'rmse': rmse,
        'mae': mae,
        'mape': mape,
        'predictions': predictions,
        'targets': targets,
        'region_ids': region_ids
    }
    
    logger.info("=== ResNet改进版模型测试结果 ===")
    logger.info(f"R² Score: {r2:.4f}")
    logger.info(f"RMSE: {rmse:.4f}")
    logger.info(f"MAE: {mae:.4f}")
    logger.info(f"MAPE: {mape:.2f}%")
    
    return results

def main():
    """主函数"""
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    from data_loader import create_data_loaders, CONFIGS
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        logger.info(f"GPU显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    # 加载数据
    config = CONFIGS['shenyang']
    train_loader, val_loader, test_loader = create_data_loaders(
        config, 
        batch_size=6,  # 稍微增加批次大小
        num_workers=0,
        max_images_per_region=3
    )
    
    # 创建改进的模型
    model = ImprovedResNetFeatureExtractor(
        model_name='resnet50',
        output_dim=128,
        dropout=0.3,
        pretrained=True
    )
    
    # 打印模型信息
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.info(f"模型参数总数: {total_params:,}")
    logger.info(f"可训练参数: {trainable_params:,}")
    
    # 创建训练器
    trainer = ImprovedResNetTrainer(
        model, 
        device=device, 
        learning_rate=0.001,  # 提高学习率
        weight_decay=1e-4
    )
    
    # 训练模型
    best_model_path = trainer.train(
        train_loader, 
        val_loader, 
        epochs=50,
        save_dir='./results/saved_models',
        warmup_epochs=5
    )
    
    # 绘制训练历史
    trainer.plot_training_history()
    
    # 测试模型
    if best_model_path:
        test_results = test_model(best_model_path, test_loader, device)
        
        # 保存结果
        os.makedirs('./results', exist_ok=True)
        results_path = './results/resnet_improved_test_results.json'
        with open(results_path, 'w') as f:
            json_results = {
                'model_type': test_results['model_type'],
                'r2_score': test_results['r2_score'],
                'rmse': test_results['rmse'],
                'mae': test_results['mae'],
                'mape': test_results['mape'],
                'predictions': [float(x) for x in test_results['predictions']],
                'targets': [float(x) for x in test_results['targets']],
                'region_ids': [int(x) for x in test_results['region_ids']]
            }
            json.dump(json_results, f, indent=2)
        
        logger.info(f"测试结果保存至: {results_path}")

if __name__ == "__main__":
    main()