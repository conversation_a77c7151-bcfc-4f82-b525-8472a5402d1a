"""
特征提取器 - 两阶段方法的第一阶段
使用预训练的深度学习模型从街景图像中提取高质量特征
支持ConvNeXt、DenseNet、MobileNet等多种模型
"""

import torch
import torch.nn as nn
import torchvision.models as models
import torchvision.transforms as transforms
import numpy as np
import os
import json
import pickle
from tqdm import tqdm
import logging
from PIL import Image

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FeatureExtractor:
    """特征提取器基类"""
    
    def __init__(self, model_name='mobilenet', device='cuda', feature_dim=1024):
        """
        Args:
            model_name: 模型名称 ('mobilenet', 'densenet', 'convnext', 'resnet')
            device: 计算设备
            feature_dim: 输出特征维度
        """
        self.model_name = model_name
        self.device = device
        self.feature_dim = feature_dim
        self.model = self._load_model()
        self.transform = self._get_transform()
        
    def _load_model(self):
        """加载预训练模型"""
        logger.info(f"加载 {self.model_name} 预训练模型...")
        
        if self.model_name.lower() == 'mobilenet':
            model = models.mobilenet_v2(pretrained=True)
            # 移除分类头，保留特征提取部分
            model.classifier = nn.Identity()
            feature_dim = 1280
            
        elif self.model_name.lower() == 'densenet':
            model = models.densenet121(pretrained=True)
            # 移除分类头
            model.classifier = nn.Identity()
            feature_dim = 1024
            
        elif self.model_name.lower() == 'resnet':
            model = models.resnet50(pretrained=True)
            # 移除分类头
            model.fc = nn.Identity()
            feature_dim = 2048
            
        elif self.model_name.lower() == 'convnext':
            try:
                # 尝试加载ConvNeXt (需要较新的torchvision版本)
                model = models.convnext_tiny(pretrained=True)
                model.classifier = nn.Identity()
                feature_dim = 768
            except AttributeError:
                logger.warning("ConvNeXt不可用，使用ResNet50替代")
                model = models.resnet50(pretrained=True)
                model.fc = nn.Identity()
                feature_dim = 2048
                
        else:
            raise ValueError(f"不支持的模型: {self.model_name}")
        
        # 添加适配层以统一特征维度
        if feature_dim != self.feature_dim:
            model = nn.Sequential(
                model,
                nn.Linear(feature_dim, self.feature_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            )
        
        model = model.to(self.device)
        model.eval()
        
        logger.info(f"模型加载完成，输出特征维度: {self.feature_dim}")
        return model
    
    def _get_transform(self):
        """获取图像预处理变换"""
        return transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
    
    def extract_single_image(self, image_path):
        """提取单张图像的特征"""
        try:
            image = Image.open(image_path).convert('RGB')
            image_tensor = self.transform(image).unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                features = self.model(image_tensor)
                features = features.cpu().numpy().flatten()
            
            return features
        except Exception as e:
            logger.warning(f"图像特征提取失败: {image_path}, 错误: {e}")
            return np.zeros(self.feature_dim)
    
    def extract_region_features(self, region_images, aggregation='mean'):
        """提取区域的特征（多张图像聚合）"""
        features_list = []
        
        for image_path in region_images:
            features = self.extract_single_image(image_path)
            features_list.append(features)
        
        if not features_list:
            return np.zeros(self.feature_dim)
        
        features_array = np.array(features_list)
        
        # 特征聚合策略
        if aggregation == 'mean':
            return np.mean(features_array, axis=0)
        elif aggregation == 'max':
            return np.max(features_array, axis=0)
        elif aggregation == 'concat':
            # 如果图像数量固定，可以拼接
            return features_array.flatten()[:self.feature_dim]
        else:
            return np.mean(features_array, axis=0)

class DatasetFeatureExtractor:
    """数据集特征提取器"""
    
    def __init__(self, config, model_name='mobilenet', feature_dim=1024, device='cuda'):
        self.config = config
        self.model_name = model_name
        self.feature_dim = feature_dim
        self.device = device
        self.extractor = FeatureExtractor(model_name, device, feature_dim)
        
        # 加载数据集信息
        self._load_dataset_info()
    
    def _load_dataset_info(self):
        """加载数据集信息"""
        logger.info("加载数据集信息...")
        
        # 加载区域信息
        with open(self.config['region2allinfo_path'], 'r', encoding='utf-8') as f:
            self.region_info = json.load(f)
        
        # 加载POI-图像映射
        with open(self.config['poi_streetview_filename_path'], 'r', encoding='utf-8') as f:
            self.poi_image_mapping = json.load(f)
        
        logger.info(f"加载完成: {len(self.region_info)} 个区域")
    
    def extract_split_features(self, split='train', max_images_per_region=5, save_path=None):
        """提取指定数据集分割的特征"""
        logger.info(f"开始提取 {split} 集特征...")
        
        # 加载数据集分割
        split_paths = {
            'train': self.config['train_idx_path'],
            'val': self.config['val_idx_path'], 
            'test': self.config['test_idx_path']
        }
        
        import pandas as pd
        split_df = pd.read_csv(split_paths[split])
        region_ids = split_df['BlockID'].tolist()
        
        features_data = {
            'features': [],
            'labels': [],
            'region_ids': [],
            'model_name': self.model_name,
            'feature_dim': self.feature_dim
        }
        
        successful_extractions = 0
        failed_extractions = 0
        
        for region_id in tqdm(region_ids, desc=f"提取{split}集特征"):
            region_str = str(region_id)
            
            # 检查区域信息
            if region_str not in self.region_info:
                failed_extractions += 1
                continue
            
            # 获取能耗标签
            energy_label = self.region_info[region_str].get('energy', None)
            if energy_label is None:
                failed_extractions += 1
                continue
            
            # 获取图像路径
            image_filenames = self.poi_image_mapping.get(region_str, [])
            if not image_filenames:
                failed_extractions += 1
                continue
            
            # 限制图像数量
            if max_images_per_region:
                image_filenames = image_filenames[:max_images_per_region]
            
            # 构建完整图像路径
            image_paths = []
            for filename in image_filenames:
                image_path = os.path.join(self.config['streetview_image_dir'], region_str, filename)
                if os.path.exists(image_path):
                    image_paths.append(image_path)
            
            if not image_paths:
                failed_extractions += 1
                continue
            
            # 提取特征
            try:
                features = self.extractor.extract_region_features(image_paths, aggregation='mean')
                
                features_data['features'].append(features)
                features_data['labels'].append(energy_label)
                features_data['region_ids'].append(region_id)
                
                successful_extractions += 1
                
            except Exception as e:
                logger.warning(f"区域 {region_id} 特征提取失败: {e}")
                failed_extractions += 1
        
        logger.info(f"特征提取完成: 成功 {successful_extractions}, 失败 {failed_extractions}")
        
        # 转换为numpy数组
        features_data['features'] = np.array(features_data['features'])
        features_data['labels'] = np.array(features_data['labels'])
        features_data['region_ids'] = np.array(features_data['region_ids'])
        
        # 保存特征
        if save_path is None:
            save_path = f'features_{self.model_name}_{split}.pkl'
        
        os.makedirs(os.path.dirname(save_path) if os.path.dirname(save_path) else '.', exist_ok=True)
        
        with open(save_path, 'wb') as f:
            pickle.dump(features_data, f)
        
        logger.info(f"特征已保存至: {save_path}")
        logger.info(f"特征形状: {features_data['features'].shape}")
        
        return features_data

def extract_all_features(config, model_names=['mobilenet', 'densenet', 'resnet'], 
                        feature_dim=1024, max_images_per_region=5, device='cuda'):
    """提取所有模型的所有数据集特征"""
    
    for model_name in model_names:
        logger.info(f"\n{'='*60}")
        logger.info(f"开始使用 {model_name} 提取特征")
        logger.info(f"{'='*60}")
        
        try:
            extractor = DatasetFeatureExtractor(
                config, model_name, feature_dim, device
            )
            
            # 提取各个数据集分割的特征
            for split in ['train', 'val', 'test']:
                save_path = f'features/features_{model_name}_{split}.pkl'
                extractor.extract_split_features(
                    split=split,
                    max_images_per_region=max_images_per_region,
                    save_path=save_path
                )
                
        except Exception as e:
            logger.error(f"{model_name} 特征提取失败: {e}")
            import traceback
            logger.error(traceback.format_exc())

if __name__ == "__main__":
    # 测试特征提取器
    from data_loader import CONFIGS
    
    config = CONFIGS['shenyang']
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 提取所有模型特征
    extract_all_features(
        config=config,
        model_names=['mobilenet', 'densenet', 'resnet'],
        feature_dim=1024,
        max_images_per_region=3,
        device=device
    )
