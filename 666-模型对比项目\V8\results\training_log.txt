2025-05-30 23:12:41,449 - __main__ - INFO - 检测到GPU: NVIDIA GeForce GTX 1050 with Max-Q Design
2025-05-30 23:12:41,449 - __main__ - INFO - 显存: 4.0 GB
2025-05-30 23:12:41,449 - __main__ - INFO - 检测到入门级GPU，推荐使用小批次训练
2025-05-30 23:12:41,449 - __main__ - INFO - 计划训练的模型: ['MobileNet', 'EfficientNet', 'ConvNeXt', 'DenseNet']
2025-05-30 23:12:41,449 - __main__ - INFO - 训练配置: batch_size=4, epochs=30, max_images=3
2025-05-30 23:12:41,461 - __main__ - INFO - 
================================================================================
2025-05-30 23:12:41,461 - __main__ - INFO - 进度: 1/4 - 开始训练 MobileNet
2025-05-30 23:12:41,462 - __main__ - INFO - ================================================================================
2025-05-30 23:12:41,463 - __main__ - INFO - 
============================================================
2025-05-30 23:12:41,463 - __main__ - INFO - 开始训练 MobileNet 模型
2025-05-30 23:12:41,463 - __main__ - INFO - ============================================================
2025-05-30 23:12:41,464 - __main__ - ERROR - MobileNet 训练失败: No module named 'mobilenet_model'
2025-05-30 23:12:41,464 - __main__ - ERROR - Traceback (most recent call last):
  File "D:\研二\能耗估算\666-模型对比项目\V8\main_runner.py", line 73, in train_single_model
    from mobilenet_model import main as mobilenet_main
ModuleNotFoundError: No module named 'mobilenet_model'

2025-05-30 23:12:41,465 - __main__ - ERROR - ❌ MobileNet 训练失败
2025-05-30 23:12:41,465 - __main__ - INFO - 等待5秒让GPU休息...
2025-05-30 23:12:46,469 - __main__ - INFO - 
================================================================================
2025-05-30 23:12:46,469 - __main__ - INFO - 进度: 2/4 - 开始训练 EfficientNet
2025-05-30 23:12:46,469 - __main__ - INFO - ================================================================================
2025-05-30 23:12:46,473 - __main__ - INFO - 
============================================================
2025-05-30 23:12:46,473 - __main__ - INFO - 开始训练 EfficientNet 模型
2025-05-30 23:12:46,473 - __main__ - INFO - ============================================================
2025-05-30 23:12:46,480 - __main__ - ERROR - EfficientNet 训练失败: No module named 'efficientnet_model'
2025-05-30 23:12:46,483 - __main__ - ERROR - Traceback (most recent call last):
  File "D:\研二\能耗估算\666-模型对比项目\V8\main_runner.py", line 76, in train_single_model
    from efficientnet_model import main as efficientnet_main
ModuleNotFoundError: No module named 'efficientnet_model'

2025-05-30 23:12:46,485 - __main__ - ERROR - ❌ EfficientNet 训练失败
2025-05-30 23:12:46,487 - __main__ - INFO - 等待5秒让GPU休息...
2025-05-30 23:12:51,499 - __main__ - INFO - 
================================================================================
2025-05-30 23:12:51,499 - __main__ - INFO - 进度: 3/4 - 开始训练 ConvNeXt
2025-05-30 23:12:51,499 - __main__ - INFO - ================================================================================
2025-05-30 23:12:51,499 - __main__ - INFO - 
============================================================
2025-05-30 23:12:51,503 - __main__ - INFO - 开始训练 ConvNeXt 模型
2025-05-30 23:12:51,503 - __main__ - INFO - ============================================================
2025-05-30 23:12:51,510 - __main__ - ERROR - ConvNeXt 训练失败: No module named 'convnext_model'
2025-05-30 23:12:51,512 - __main__ - ERROR - Traceback (most recent call last):
  File "D:\研二\能耗估算\666-模型对比项目\V8\main_runner.py", line 85, in train_single_model
    from convnext_model import main as convnext_main
ModuleNotFoundError: No module named 'convnext_model'

2025-05-30 23:12:51,513 - __main__ - ERROR - ❌ ConvNeXt 训练失败
2025-05-30 23:12:51,515 - __main__ - INFO - 等待5秒让GPU休息...
2025-05-30 23:12:56,523 - __main__ - INFO - 
================================================================================
2025-05-30 23:12:56,523 - __main__ - INFO - 进度: 4/4 - 开始训练 DenseNet
2025-05-30 23:12:56,523 - __main__ - INFO - ================================================================================
2025-05-30 23:12:56,523 - __main__ - INFO - 
============================================================
2025-05-30 23:12:56,523 - __main__ - INFO - 开始训练 DenseNet 模型
2025-05-30 23:12:56,527 - __main__ - INFO - ============================================================
2025-05-30 23:12:56,527 - __main__ - ERROR - DenseNet 训练失败: No module named 'densenet_model'
2025-05-30 23:12:56,527 - __main__ - ERROR - Traceback (most recent call last):
  File "D:\研二\能耗估算\666-模型对比项目\V8\main_runner.py", line 88, in train_single_model
    from densenet_model import main as densenet_main
ModuleNotFoundError: No module named 'densenet_model'

2025-05-30 23:12:56,532 - __main__ - ERROR - ❌ DenseNet 训练失败
2025-05-30 23:12:56,532 - __main__ - INFO - 
================================================================================
2025-05-30 23:12:56,533 - __main__ - INFO - 收集和分析结果...
2025-05-30 23:12:56,534 - __main__ - INFO - ================================================================================
2025-05-30 23:12:56,535 - __main__ - WARNING - 没有找到任何测试结果
2025-05-30 23:12:56,538 - __main__ - INFO - 
================================================================================
2025-05-30 23:12:56,539 - __main__ - INFO - 训练总结
2025-05-30 23:12:56,540 - __main__ - INFO - ================================================================================
2025-05-30 23:12:56,541 - __main__ - INFO - 总训练时间: 0.00 小时
2025-05-30 23:12:56,541 - __main__ - INFO - 成功训练的模型 (0): []
2025-05-30 23:12:56,542 - __main__ - INFO - 训练失败的模型 (4): ['MobileNet', 'EfficientNet', 'ConvNeXt', 'DenseNet']
2025-05-30 23:21:37,130 - __main__ - INFO - 检测到GPU: NVIDIA GeForce GTX 1050 with Max-Q Design
2025-05-30 23:21:37,130 - __main__ - INFO - 显存: 4.0 GB
2025-05-30 23:21:37,131 - __main__ - INFO - 检测到入门级GPU，推荐使用小批次训练
2025-05-30 23:21:37,131 - __main__ - INFO - 计划训练的模型: ['MobileNet', 'EfficientNet', 'ConvNeXt', 'DenseNet']
2025-05-30 23:21:37,131 - __main__ - INFO - 训练配置: batch_size=4, epochs=30, max_images=3
2025-05-30 23:21:37,133 - __main__ - INFO - 
================================================================================
2025-05-30 23:21:37,133 - __main__ - INFO - 进度: 1/4 - 开始训练 MobileNet
2025-05-30 23:21:37,133 - __main__ - INFO - ================================================================================
2025-05-30 23:21:37,134 - __main__ - INFO - 
============================================================
2025-05-30 23:21:37,135 - __main__ - INFO - 开始训练 MobileNet 模型
2025-05-30 23:21:37,135 - __main__ - INFO - ============================================================
2025-05-30 23:21:37,137 - __main__ - ERROR - MobileNet 训练失败: No module named 'mobilenet_model'
2025-05-30 23:21:37,137 - __main__ - ERROR - Traceback (most recent call last):
  File "main_runner.py", line 73, in train_single_model
    from mobilenet_model import main as mobilenet_main
ModuleNotFoundError: No module named 'mobilenet_model'

2025-05-30 23:21:37,138 - __main__ - ERROR - ❌ MobileNet 训练失败
2025-05-30 23:21:37,138 - __main__ - INFO - 等待5秒让GPU休息...
2025-05-30 23:21:42,146 - __main__ - INFO - 
================================================================================
2025-05-30 23:21:42,148 - __main__ - INFO - 进度: 2/4 - 开始训练 EfficientNet
2025-05-30 23:21:42,150 - __main__ - INFO - ================================================================================
2025-05-30 23:21:42,151 - __main__ - INFO - 
============================================================
2025-05-30 23:21:42,154 - __main__ - INFO - 开始训练 EfficientNet 模型
2025-05-30 23:21:42,155 - __main__ - INFO - ============================================================
2025-05-30 23:21:42,163 - __main__ - ERROR - EfficientNet 训练失败: No module named 'efficientnet_model'
2025-05-30 23:21:42,166 - __main__ - ERROR - Traceback (most recent call last):
  File "main_runner.py", line 76, in train_single_model
    from efficientnet_model import main as efficientnet_main
ModuleNotFoundError: No module named 'efficientnet_model'

2025-05-30 23:21:42,168 - __main__ - ERROR - ❌ EfficientNet 训练失败
2025-05-30 23:21:42,170 - __main__ - INFO - 等待5秒让GPU休息...
2025-05-30 23:21:47,180 - __main__ - INFO - 
================================================================================
2025-05-30 23:21:47,182 - __main__ - INFO - 进度: 3/4 - 开始训练 ConvNeXt
2025-05-30 23:21:47,182 - __main__ - INFO - ================================================================================
2025-05-30 23:21:47,183 - __main__ - INFO - 
============================================================
2025-05-30 23:21:47,184 - __main__ - INFO - 开始训练 ConvNeXt 模型
2025-05-30 23:21:47,185 - __main__ - INFO - ============================================================
2025-05-30 23:21:47,188 - __main__ - ERROR - ConvNeXt 训练失败: No module named 'convnext_model'
2025-05-30 23:21:47,190 - __main__ - ERROR - Traceback (most recent call last):
  File "main_runner.py", line 85, in train_single_model
    from convnext_model import main as convnext_main
ModuleNotFoundError: No module named 'convnext_model'

2025-05-30 23:21:47,192 - __main__ - ERROR - ❌ ConvNeXt 训练失败
2025-05-30 23:21:47,192 - __main__ - INFO - 等待5秒让GPU休息...
2025-05-30 23:21:52,207 - __main__ - INFO - 
================================================================================
2025-05-30 23:21:52,208 - __main__ - INFO - 进度: 4/4 - 开始训练 DenseNet
2025-05-30 23:21:52,208 - __main__ - INFO - ================================================================================
2025-05-30 23:21:52,209 - __main__ - INFO - 
============================================================
2025-05-30 23:21:52,210 - __main__ - INFO - 开始训练 DenseNet 模型
2025-05-30 23:21:52,211 - __main__ - INFO - ============================================================
2025-05-30 23:21:52,215 - __main__ - ERROR - DenseNet 训练失败: No module named 'densenet_model'
2025-05-30 23:21:52,216 - __main__ - ERROR - Traceback (most recent call last):
  File "main_runner.py", line 88, in train_single_model
    from densenet_model import main as densenet_main
ModuleNotFoundError: No module named 'densenet_model'

2025-05-30 23:21:52,218 - __main__ - ERROR - ❌ DenseNet 训练失败
2025-05-30 23:21:52,219 - __main__ - INFO - 
================================================================================
2025-05-30 23:21:52,220 - __main__ - INFO - 收集和分析结果...
2025-05-30 23:21:52,221 - __main__ - INFO - ================================================================================
2025-05-30 23:21:52,222 - __main__ - WARNING - 没有找到任何测试结果
2025-05-30 23:21:52,225 - __main__ - INFO - 
================================================================================
2025-05-30 23:21:52,226 - __main__ - INFO - 训练总结
2025-05-30 23:21:52,227 - __main__ - INFO - ================================================================================
2025-05-30 23:21:52,228 - __main__ - INFO - 总训练时间: 0.00 小时
2025-05-30 23:21:52,229 - __main__ - INFO - 成功训练的模型 (0): []
2025-05-30 23:21:52,229 - __main__ - INFO - 训练失败的模型 (4): ['MobileNet', 'EfficientNet', 'ConvNeXt', 'DenseNet']
2025-05-30 23:23:43,736 - __main__ - INFO - 检测到GPU: NVIDIA GeForce GTX 1050 with Max-Q Design
2025-05-30 23:23:43,736 - __main__ - INFO - 显存: 4.0 GB
2025-05-30 23:23:43,737 - __main__ - INFO - 检测到入门级GPU，推荐使用小批次训练
2025-05-30 23:23:43,737 - __main__ - INFO - 计划训练的模型: ['MobileNet', 'EfficientNet', 'ConvNeXt', 'DenseNet']
2025-05-30 23:23:43,737 - __main__ - INFO - 训练配置: batch_size=4, epochs=30, max_images=3
2025-05-30 23:23:43,738 - __main__ - INFO - 
================================================================================
2025-05-30 23:23:43,739 - __main__ - INFO - 进度: 1/4 - 开始训练 MobileNet
2025-05-30 23:23:43,739 - __main__ - INFO - ================================================================================
2025-05-30 23:23:43,739 - __main__ - INFO - 
============================================================
2025-05-30 23:23:43,740 - __main__ - INFO - 开始训练 MobileNet 模型
2025-05-30 23:23:43,740 - __main__ - INFO - ============================================================
2025-05-30 23:23:46,554 - models.mobilenet_model - INFO - 使用设备: cuda
2025-05-30 23:23:46,554 - models.mobilenet_model - INFO - GPU显存: 4.0 GB
2025-05-30 23:23:46,717 - models.mobilenet_model - INFO - 模型参数总数: 4,337,321
2025-05-30 23:23:46,717 - models.mobilenet_model - INFO - 可训练参数: 4,337,321
2025-05-30 23:23:46,717 - models.mobilenet_model - INFO - 模型大小: 16.5 MB
2025-05-30 23:23:46,718 - data_loader - INFO - 加载区域信息和能耗标签...
2025-05-30 23:23:46,735 - data_loader - INFO - train集包含 97 个区域
2025-05-30 23:23:46,736 - data_loader - INFO - 加载POI-街景图像映射...
2025-05-30 23:23:46,776 - data_loader - INFO - 成功构建 97 个有效样本
2025-05-30 23:23:46,777 - data_loader - INFO - 加载区域信息和能耗标签...
2025-05-30 23:23:46,795 - data_loader - INFO - val集包含 12 个区域
2025-05-30 23:23:46,795 - data_loader - INFO - 加载POI-街景图像映射...
2025-05-30 23:23:46,801 - data_loader - INFO - 成功构建 12 个有效样本
2025-05-30 23:23:46,802 - data_loader - INFO - 加载区域信息和能耗标签...
2025-05-30 23:23:46,902 - data_loader - INFO - test集包含 13 个区域
2025-05-30 23:23:46,902 - data_loader - INFO - 加载POI-街景图像映射...
2025-05-30 23:23:46,908 - data_loader - INFO - 成功构建 13 个有效样本
2025-05-30 23:23:46,909 - data_loader - INFO - 数据加载器创建完成:
2025-05-30 23:23:46,909 - data_loader - INFO -   训练集: 97 样本
2025-05-30 23:23:46,909 - data_loader - INFO -   验证集: 12 样本
2025-05-30 23:23:46,909 - data_loader - INFO -   测试集: 13 样本
2025-05-30 23:23:47,411 - models.mobilenet_model - INFO - 开始训练MobileNet模型，总共 50 个epoch
2025-05-30 23:23:56,272 - models.mobilenet_model - INFO - 批次 0/13, 损失: 1.9832, LR: 0.000162
2025-05-30 23:24:02,185 - models.mobilenet_model - INFO - 批次 10/13, 损失: 2.0857, LR: 0.000433
2025-05-30 23:24:02,919 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:24:03,379 - models.mobilenet_model - INFO - Epoch 1/50:
2025-05-30 23:24:03,379 - models.mobilenet_model - INFO -   学习率: 0.000160
2025-05-30 23:24:03,380 - models.mobilenet_model - INFO -   训练损失: 2.5799, 训练R²: -172.8411
2025-05-30 23:24:03,380 - models.mobilenet_model - INFO -   验证损失: 1.2385, 验证R²: -107.3230
2025-05-30 23:24:03,380 - models.mobilenet_model - INFO -   验证RMSE: 3.2638, 验证MAE: 2.5125
2025-05-30 23:24:03,560 - models.mobilenet_model - INFO - 保存最佳模型: ./results/saved_models\shenyang_MobileNet.pt (验证损失: 1.2385)
2025-05-30 23:24:04,189 - models.mobilenet_model - INFO - 批次 0/13, 损失: 2.2061, LR: 0.000538
2025-05-30 23:24:10,738 - models.mobilenet_model - INFO - 批次 10/13, 损失: 1.8917, LR: 0.001259
2025-05-30 23:24:11,475 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:24:11,901 - models.mobilenet_model - INFO - Epoch 2/50:
2025-05-30 23:24:11,901 - models.mobilenet_model - INFO -   学习率: 0.000484
2025-05-30 23:24:11,902 - models.mobilenet_model - INFO -   训练损失: 2.5071, 训练R²: -160.5596
2025-05-30 23:24:11,902 - models.mobilenet_model - INFO -   验证损失: 1.0465, 验证R²: -66.8142
2025-05-30 23:24:11,903 - models.mobilenet_model - INFO -   验证RMSE: 2.5824, 验证MAE: 2.1846
2025-05-30 23:24:12,189 - models.mobilenet_model - INFO - 保存最佳模型: ./results/saved_models\shenyang_MobileNet.pt (验证损失: 1.0465)
2025-05-30 23:24:12,912 - models.mobilenet_model - INFO - 批次 0/13, 损失: 2.3133, LR: 0.001433
2025-05-30 23:24:19,621 - models.mobilenet_model - INFO - 批次 10/13, 损失: 2.3557, LR: 0.002362
2025-05-30 23:24:20,347 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:24:20,782 - models.mobilenet_model - INFO - Epoch 3/50:
2025-05-30 23:24:20,783 - models.mobilenet_model - INFO -   学习率: 0.001345
2025-05-30 23:24:20,784 - models.mobilenet_model - INFO -   训练损失: 2.2752, 训练R²: -156.1254
2025-05-30 23:24:20,784 - models.mobilenet_model - INFO -   验证损失: 2.7583, 验证R²: -481.8196
2025-05-30 23:24:20,784 - models.mobilenet_model - INFO -   验证RMSE: 6.8905, 验证MAE: 5.7174
2025-05-30 23:24:21,456 - models.mobilenet_model - INFO - 批次 0/13, 损失: 2.0553, LR: 0.002547
2025-05-30 23:27:24,106 - __main__ - INFO - 检测到GPU: NVIDIA GeForce GTX 1050 with Max-Q Design
2025-05-30 23:27:24,107 - __main__ - INFO - 显存: 4.0 GB
2025-05-30 23:27:24,107 - __main__ - INFO - 检测到入门级GPU，推荐使用小批次训练
2025-05-30 23:27:24,107 - __main__ - INFO - 计划训练的模型: ['MobileNet', 'EfficientNet', 'ConvNeXt', 'DenseNet', 'ResNet', 'ViT', 'Swin', 'MLP']
2025-05-30 23:27:24,108 - __main__ - INFO - 训练配置: batch_size=4, epochs=30, max_images=3
2025-05-30 23:27:24,109 - __main__ - INFO - 
================================================================================
2025-05-30 23:27:24,110 - __main__ - INFO - 进度: 1/8 - 开始训练 MobileNet
2025-05-30 23:27:24,110 - __main__ - INFO - ================================================================================
2025-05-30 23:27:24,110 - __main__ - INFO - 
============================================================
2025-05-30 23:27:24,111 - __main__ - INFO - 开始训练 MobileNet 模型
2025-05-30 23:27:24,111 - __main__ - INFO - ============================================================
2025-05-30 23:27:25,871 - models.mobilenet_model - INFO - 使用设备: cuda
2025-05-30 23:27:25,871 - models.mobilenet_model - INFO - GPU显存: 4.0 GB
2025-05-30 23:27:25,987 - models.mobilenet_model - INFO - 模型参数总数: 4,337,321
2025-05-30 23:27:25,987 - models.mobilenet_model - INFO - 可训练参数: 4,337,321
2025-05-30 23:27:25,988 - models.mobilenet_model - INFO - 模型大小: 16.5 MB
2025-05-30 23:27:25,988 - data_loader - INFO - 加载区域信息和能耗标签...
2025-05-30 23:27:26,003 - data_loader - INFO - train集包含 97 个区域
2025-05-30 23:27:26,003 - data_loader - INFO - 加载POI-街景图像映射...
2025-05-30 23:27:26,046 - data_loader - INFO - 成功构建 97 个有效样本
2025-05-30 23:27:26,047 - data_loader - INFO - 加载区域信息和能耗标签...
2025-05-30 23:27:26,062 - data_loader - INFO - val集包含 12 个区域
2025-05-30 23:27:26,063 - data_loader - INFO - 加载POI-街景图像映射...
2025-05-30 23:27:26,070 - data_loader - INFO - 成功构建 12 个有效样本
2025-05-30 23:27:26,071 - data_loader - INFO - 加载区域信息和能耗标签...
2025-05-30 23:27:26,158 - data_loader - INFO - test集包含 13 个区域
2025-05-30 23:27:26,158 - data_loader - INFO - 加载POI-街景图像映射...
2025-05-30 23:27:26,163 - data_loader - INFO - 成功构建 13 个有效样本
2025-05-30 23:27:26,163 - data_loader - INFO - 数据加载器创建完成:
2025-05-30 23:27:26,164 - data_loader - INFO -   训练集: 97 样本
2025-05-30 23:27:26,164 - data_loader - INFO -   验证集: 12 样本
2025-05-30 23:27:26,164 - data_loader - INFO -   测试集: 13 样本
2025-05-30 23:27:26,658 - models.mobilenet_model - INFO - 开始训练MobileNet模型，总共 50 个epoch
2025-05-30 23:27:30,039 - models.mobilenet_model - INFO - 批次 0/13, 损失: 1.4695, LR: 0.000162
2025-05-30 23:27:34,570 - models.mobilenet_model - INFO - 批次 10/13, 损失: 1.4398, LR: 0.000433
2025-05-30 23:27:35,291 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:27:35,716 - models.mobilenet_model - INFO - Epoch 1/50:
2025-05-30 23:27:35,716 - models.mobilenet_model - INFO -   学习率: 0.000160
2025-05-30 23:27:35,716 - models.mobilenet_model - INFO -   训练损失: 1.8122, 训练R²: -87.2082
2025-05-30 23:27:35,716 - models.mobilenet_model - INFO -   验证损失: 1.8294, 验证R²: -270.7963
2025-05-30 23:27:35,717 - models.mobilenet_model - INFO -   验证RMSE: 5.1699, 验证MAE: 4.0698
2025-05-30 23:27:35,893 - models.mobilenet_model - INFO - 保存最佳模型: ./results/saved_models\shenyang_MobileNet.pt (验证损失: 1.8294)
2025-05-30 23:27:36,411 - models.mobilenet_model - INFO - 批次 0/13, 损失: 1.6577, LR: 0.000538
2025-05-30 23:27:42,098 - models.mobilenet_model - INFO - 批次 10/13, 损失: 2.6102, LR: 0.001259
2025-05-30 23:27:42,828 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:27:43,341 - models.mobilenet_model - INFO - Epoch 2/50:
2025-05-30 23:27:43,342 - models.mobilenet_model - INFO -   学习率: 0.000484
2025-05-30 23:27:43,342 - models.mobilenet_model - INFO -   训练损失: 1.8122, 训练R²: -86.4066
2025-05-30 23:27:43,343 - models.mobilenet_model - INFO -   验证损失: 1.4015, 验证R²: -102.6112
2025-05-30 23:27:43,343 - models.mobilenet_model - INFO -   验证RMSE: 3.1920, 验证MAE: 2.5207
2025-05-30 23:27:43,609 - models.mobilenet_model - INFO - 保存最佳模型: ./results/saved_models\shenyang_MobileNet.pt (验证损失: 1.4015)
2025-05-30 23:27:44,280 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.8857, LR: 0.001433
2025-05-30 23:27:50,987 - models.mobilenet_model - INFO - 批次 10/13, 损失: 1.4560, LR: 0.002362
2025-05-30 23:27:51,713 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:27:52,126 - models.mobilenet_model - INFO - Epoch 3/50:
2025-05-30 23:27:52,127 - models.mobilenet_model - INFO -   学习率: 0.001345
2025-05-30 23:27:52,127 - models.mobilenet_model - INFO -   训练损失: 1.5362, 训练R²: -69.7981
2025-05-30 23:27:52,127 - models.mobilenet_model - INFO -   验证损失: 2.8193, 验证R²: -1024.7413
2025-05-30 23:27:52,128 - models.mobilenet_model - INFO -   验证RMSE: 10.0433, 验证MAE: 6.3719
2025-05-30 23:27:52,829 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.8952, LR: 0.002547
2025-05-30 23:27:59,536 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.8052, LR: 0.003369
2025-05-30 23:28:00,267 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:28:00,772 - models.mobilenet_model - INFO - Epoch 4/50:
2025-05-30 23:28:00,773 - models.mobilenet_model - INFO -   学习率: 0.002455
2025-05-30 23:28:00,773 - models.mobilenet_model - INFO -   训练损失: 1.2580, 训练R²: -44.7505
2025-05-30 23:28:00,774 - models.mobilenet_model - INFO -   验证损失: 3.0775, 验证R²: -729.8674
2025-05-30 23:28:00,774 - models.mobilenet_model - INFO -   验证RMSE: 8.4777, 验证MAE: 6.6251
2025-05-30 23:28:01,388 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.7502, LR: 0.003503
2025-05-30 23:28:08,082 - models.mobilenet_model - INFO - 批次 10/13, 损失: 1.1418, LR: 0.003942
2025-05-30 23:28:08,809 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:28:09,247 - models.mobilenet_model - INFO - Epoch 5/50:
2025-05-30 23:28:09,248 - models.mobilenet_model - INFO -   学习率: 0.003438
2025-05-30 23:28:09,248 - models.mobilenet_model - INFO -   训练损失: 0.8948, 训练R²: -25.1229
2025-05-30 23:28:09,248 - models.mobilenet_model - INFO -   验证损失: 8.4234, 验证R²: -3209.6763
2025-05-30 23:28:09,248 - models.mobilenet_model - INFO -   验证RMSE: 17.7687, 验证MAE: 17.1342
2025-05-30 23:28:09,930 - models.mobilenet_model - INFO - 批次 0/13, 损失: 1.0269, LR: 0.003979
2025-05-30 23:28:16,631 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.9529, LR: 0.003999
2025-05-30 23:28:17,359 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:28:17,783 - models.mobilenet_model - INFO - Epoch 6/50:
2025-05-30 23:28:17,784 - models.mobilenet_model - INFO -   学习率: 0.003963
2025-05-30 23:28:17,784 - models.mobilenet_model - INFO -   训练损失: 0.7991, 训练R²: -20.8437
2025-05-30 23:28:17,784 - models.mobilenet_model - INFO -   验证损失: 1.5612, 验证R²: -128.1690
2025-05-30 23:28:17,785 - models.mobilenet_model - INFO -   验证RMSE: 3.5640, 验证MAE: 3.1968
2025-05-30 23:28:18,478 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.7132, LR: 0.003998
2025-05-30 23:28:25,178 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.4765, LR: 0.003990
2025-05-30 23:28:25,927 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:28:26,329 - models.mobilenet_model - INFO - Epoch 7/50:
2025-05-30 23:28:26,330 - models.mobilenet_model - INFO -   学习率: 0.003998
2025-05-30 23:28:26,330 - models.mobilenet_model - INFO -   训练损失: 0.7594, 训练R²: -18.3432
2025-05-30 23:28:26,331 - models.mobilenet_model - INFO -   验证损失: 1.2920, 验证R²: -171.8215
2025-05-30 23:28:26,332 - models.mobilenet_model - INFO -   验证RMSE: 4.1225, 验证MAE: 2.9993
2025-05-30 23:28:26,598 - models.mobilenet_model - INFO - 保存最佳模型: ./results/saved_models\shenyang_MobileNet.pt (验证损失: 1.2920)
2025-05-30 23:28:27,202 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.7594, LR: 0.003987
2025-05-30 23:28:33,891 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.2732, LR: 0.003972
2025-05-30 23:28:34,630 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:28:35,046 - models.mobilenet_model - INFO - Epoch 8/50:
2025-05-30 23:28:35,047 - models.mobilenet_model - INFO -   学习率: 0.003988
2025-05-30 23:28:35,047 - models.mobilenet_model - INFO -   训练损失: 0.6166, 训练R²: -13.2975
2025-05-30 23:28:35,048 - models.mobilenet_model - INFO -   验证损失: 2.2460, 验证R²: -717.6388
2025-05-30 23:28:35,048 - models.mobilenet_model - INFO -   验证RMSE: 8.4065, 验证MAE: 5.5473
2025-05-30 23:28:35,737 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.6955, LR: 0.003969
2025-05-30 23:28:42,440 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.8863, LR: 0.003947
2025-05-30 23:28:43,171 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:28:43,602 - models.mobilenet_model - INFO - Epoch 9/50:
2025-05-30 23:28:43,602 - models.mobilenet_model - INFO -   学习率: 0.003971
2025-05-30 23:28:43,603 - models.mobilenet_model - INFO -   训练损失: 0.5044, 训练R²: -10.9705
2025-05-30 23:28:43,603 - models.mobilenet_model - INFO -   验证损失: 0.9405, 验证R²: -98.4193
2025-05-30 23:28:43,604 - models.mobilenet_model - INFO -   验证RMSE: 3.1268, 验证MAE: 2.2972
2025-05-30 23:28:43,884 - models.mobilenet_model - INFO - 保存最佳模型: ./results/saved_models\shenyang_MobileNet.pt (验证损失: 0.9405)
2025-05-30 23:28:44,615 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.6089, LR: 0.003942
2025-05-30 23:28:51,324 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.5765, LR: 0.003913
2025-05-30 23:28:52,065 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:28:52,483 - models.mobilenet_model - INFO - Epoch 10/50:
2025-05-30 23:28:52,483 - models.mobilenet_model - INFO -   学习率: 0.003944
2025-05-30 23:28:52,484 - models.mobilenet_model - INFO -   训练损失: 0.4401, 训练R²: -7.6272
2025-05-30 23:28:52,484 - models.mobilenet_model - INFO -   验证损失: 0.1420, 验证R²: -4.3689
2025-05-30 23:28:52,484 - models.mobilenet_model - INFO -   验证RMSE: 0.7266, 验证MAE: 0.5422
2025-05-30 23:28:52,765 - models.mobilenet_model - INFO - 保存最佳模型: ./results/saved_models\shenyang_MobileNet.pt (验证损失: 0.1420)
2025-05-30 23:28:53,346 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.2621, LR: 0.003907
2025-05-30 23:29:00,039 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.3247, LR: 0.003872
2025-05-30 23:29:00,803 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:29:01,201 - models.mobilenet_model - INFO - Epoch 11/50:
2025-05-30 23:29:01,201 - models.mobilenet_model - INFO -   学习率: 0.003910
2025-05-30 23:29:01,202 - models.mobilenet_model - INFO -   训练损失: 0.3698, 训练R²: -5.0639
2025-05-30 23:29:01,202 - models.mobilenet_model - INFO -   验证损失: 0.0507, 验证R²: -0.4117
2025-05-30 23:29:01,202 - models.mobilenet_model - INFO -   验证RMSE: 0.3726, 验证MAE: 0.2750
2025-05-30 23:29:01,473 - models.mobilenet_model - INFO - 保存最佳模型: ./results/saved_models\shenyang_MobileNet.pt (验证损失: 0.0507)
2025-05-30 23:29:02,070 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.3160, LR: 0.003864
2025-05-30 23:29:08,758 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.0621, LR: 0.003823
2025-05-30 23:29:09,510 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:29:09,915 - models.mobilenet_model - INFO - Epoch 12/50:
2025-05-30 23:29:09,916 - models.mobilenet_model - INFO -   学习率: 0.003868
2025-05-30 23:29:09,916 - models.mobilenet_model - INFO -   训练损失: 0.3453, 训练R²: -4.8728
2025-05-30 23:29:09,916 - models.mobilenet_model - INFO -   验证损失: 0.1314, 验证R²: -2.3041
2025-05-30 23:29:09,917 - models.mobilenet_model - INFO -   验证RMSE: 0.5700, 验证MAE: 0.4091
2025-05-30 23:29:10,601 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.3547, LR: 0.003814
2025-05-30 23:29:17,314 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.2910, LR: 0.003766
2025-05-30 23:29:18,057 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:29:18,471 - models.mobilenet_model - INFO - Epoch 13/50:
2025-05-30 23:29:18,471 - models.mobilenet_model - INFO -   学习率: 0.003818
2025-05-30 23:29:18,472 - models.mobilenet_model - INFO -   训练损失: 0.3449, 训练R²: -5.0737
2025-05-30 23:29:18,472 - models.mobilenet_model - INFO -   验证损失: 0.1372, 验证R²: -3.4386
2025-05-30 23:29:18,472 - models.mobilenet_model - INFO -   验证RMSE: 0.6607, 验证MAE: 0.4811
2025-05-30 23:29:19,158 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.3808, LR: 0.003756
2025-05-30 23:29:25,863 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.1756, LR: 0.003702
2025-05-30 23:29:26,593 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:29:27,019 - models.mobilenet_model - INFO - Epoch 14/50:
2025-05-30 23:29:27,019 - models.mobilenet_model - INFO -   学习率: 0.003761
2025-05-30 23:29:27,019 - models.mobilenet_model - INFO -   训练损失: 0.2360, 训练R²: -2.0754
2025-05-30 23:29:27,020 - models.mobilenet_model - INFO -   验证损失: 0.1010, 验证R²: -1.3857
2025-05-30 23:29:27,020 - models.mobilenet_model - INFO -   验证RMSE: 0.4844, 验证MAE: 0.3959
2025-05-30 23:29:27,714 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.4011, LR: 0.003690
2025-05-30 23:29:34,416 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.2787, LR: 0.003631
2025-05-30 23:29:35,150 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:29:35,566 - models.mobilenet_model - INFO - Epoch 15/50:
2025-05-30 23:29:35,566 - models.mobilenet_model - INFO -   学习率: 0.003696
2025-05-30 23:29:35,567 - models.mobilenet_model - INFO -   训练损失: 0.3225, 训练R²: -3.9962
2025-05-30 23:29:35,567 - models.mobilenet_model - INFO -   验证损失: 0.0362, 验证R²: 0.1652
2025-05-30 23:29:35,568 - models.mobilenet_model - INFO -   验证RMSE: 0.2865, 验证MAE: 0.2233
2025-05-30 23:29:35,832 - models.mobilenet_model - INFO - 保存最佳模型: ./results/saved_models\shenyang_MobileNet.pt (验证损失: 0.0362)
2025-05-30 23:29:36,436 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.2056, LR: 0.003618
2025-05-30 23:29:43,126 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.2833, LR: 0.003553
2025-05-30 23:29:43,870 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:29:44,285 - models.mobilenet_model - INFO - Epoch 16/50:
2025-05-30 23:29:44,286 - models.mobilenet_model - INFO -   学习率: 0.003624
2025-05-30 23:29:44,286 - models.mobilenet_model - INFO -   训练损失: 0.2699, 训练R²: -3.1193
2025-05-30 23:29:44,286 - models.mobilenet_model - INFO -   验证损失: 0.6128, 验证R²: -28.7739
2025-05-30 23:29:44,287 - models.mobilenet_model - INFO -   验证RMSE: 1.7111, 验证MAE: 1.3340
2025-05-30 23:29:44,970 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.4642, LR: 0.003539
2025-05-30 23:29:51,680 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.2680, LR: 0.003468
2025-05-30 23:29:52,408 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:29:52,832 - models.mobilenet_model - INFO - Epoch 17/50:
2025-05-30 23:29:52,832 - models.mobilenet_model - INFO -   学习率: 0.003546
2025-05-30 23:29:52,833 - models.mobilenet_model - INFO -   训练损失: 0.2666, 训练R²: -3.3638
2025-05-30 23:29:52,833 - models.mobilenet_model - INFO -   验证损失: 0.3408, 验证R²: -9.4787
2025-05-30 23:29:52,834 - models.mobilenet_model - INFO -   验证RMSE: 1.0151, 验证MAE: 0.8883
2025-05-30 23:29:53,522 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.4943, LR: 0.003454
2025-05-30 23:30:00,224 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.3838, LR: 0.003378
2025-05-30 23:30:00,960 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:30:01,391 - models.mobilenet_model - INFO - Epoch 18/50:
2025-05-30 23:30:01,392 - models.mobilenet_model - INFO -   学习率: 0.003461
2025-05-30 23:30:01,392 - models.mobilenet_model - INFO -   训练损失: 0.2580, 训练R²: -2.8917
2025-05-30 23:30:01,393 - models.mobilenet_model - INFO -   验证损失: 0.0878, 验证R²: -1.2712
2025-05-30 23:30:01,393 - models.mobilenet_model - INFO -   验证RMSE: 0.4726, 验证MAE: 0.3756
2025-05-30 23:30:02,073 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.2235, LR: 0.003362
2025-05-30 23:30:08,776 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.2736, LR: 0.003281
2025-05-30 23:30:09,509 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:30:09,938 - models.mobilenet_model - INFO - Epoch 19/50:
2025-05-30 23:30:09,939 - models.mobilenet_model - INFO -   学习率: 0.003370
2025-05-30 23:30:09,939 - models.mobilenet_model - INFO -   训练损失: 0.2494, 训练R²: -2.1842
2025-05-30 23:30:09,940 - models.mobilenet_model - INFO -   验证损失: 0.3986, 验证R²: -22.9073
2025-05-30 23:30:09,940 - models.mobilenet_model - INFO -   验证RMSE: 1.5333, 验证MAE: 1.0962
2025-05-30 23:30:10,626 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.2473, LR: 0.003265
2025-05-30 23:30:17,992 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.3308, LR: 0.003180
2025-05-30 23:30:18,885 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:30:19,307 - models.mobilenet_model - INFO - Epoch 20/50:
2025-05-30 23:30:19,308 - models.mobilenet_model - INFO -   学习率: 0.003273
2025-05-30 23:30:19,308 - models.mobilenet_model - INFO -   训练损失: 0.3073, 训练R²: -4.2483
2025-05-30 23:30:19,308 - models.mobilenet_model - INFO -   验证损失: 0.0866, 验证R²: -1.0985
2025-05-30 23:30:19,309 - models.mobilenet_model - INFO -   验证RMSE: 0.4543, 验证MAE: 0.3746
2025-05-30 23:30:19,999 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.2912, LR: 0.003163
2025-05-30 23:30:26,699 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.1480, LR: 0.003073
2025-05-30 23:30:27,428 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:30:27,851 - models.mobilenet_model - INFO - Epoch 21/50:
2025-05-30 23:30:27,851 - models.mobilenet_model - INFO -   学习率: 0.003171
2025-05-30 23:30:27,852 - models.mobilenet_model - INFO -   训练损失: 0.2517, 训练R²: -2.6461
2025-05-30 23:30:27,852 - models.mobilenet_model - INFO -   验证损失: 0.0610, 验证R²: -0.5392
2025-05-30 23:30:27,852 - models.mobilenet_model - INFO -   验证RMSE: 0.3890, 验证MAE: 0.3003
2025-05-30 23:30:28,542 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.3489, LR: 0.003055
2025-05-30 23:30:35,249 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.4991, LR: 0.002963
2025-05-30 23:30:35,977 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:30:36,409 - models.mobilenet_model - INFO - Epoch 22/50:
2025-05-30 23:30:36,410 - models.mobilenet_model - INFO -   学习率: 0.003064
2025-05-30 23:30:36,410 - models.mobilenet_model - INFO -   训练损失: 0.2456, 训练R²: -3.8820
2025-05-30 23:30:36,411 - models.mobilenet_model - INFO -   验证损失: 0.0575, 验证R²: -0.5201
2025-05-30 23:30:36,411 - models.mobilenet_model - INFO -   验证RMSE: 0.3866, 验证MAE: 0.2835
2025-05-30 23:30:37,096 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.0472, LR: 0.002944
2025-05-30 23:30:43,796 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.2818, LR: 0.002848
2025-05-30 23:30:44,526 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:30:44,945 - models.mobilenet_model - INFO - Epoch 23/50:
2025-05-30 23:30:44,945 - models.mobilenet_model - INFO -   学习率: 0.002953
2025-05-30 23:30:44,945 - models.mobilenet_model - INFO -   训练损失: 0.2321, 训练R²: -2.8803
2025-05-30 23:30:44,946 - models.mobilenet_model - INFO -   验证损失: 0.0460, 验证R²: -0.2270
2025-05-30 23:30:44,946 - models.mobilenet_model - INFO -   验证RMSE: 0.3474, 验证MAE: 0.2565
2025-05-30 23:30:45,641 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.3939, LR: 0.002828
2025-05-30 23:30:52,338 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.3457, LR: 0.002729
2025-05-30 23:30:53,065 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:30:53,488 - models.mobilenet_model - INFO - Epoch 24/50:
2025-05-30 23:30:53,489 - models.mobilenet_model - INFO -   学习率: 0.002838
2025-05-30 23:30:53,489 - models.mobilenet_model - INFO -   训练损失: 0.2228, 训练R²: -1.9987
2025-05-30 23:30:53,489 - models.mobilenet_model - INFO -   验证损失: 0.0549, 验证R²: -0.4516
2025-05-30 23:30:53,490 - models.mobilenet_model - INFO -   验证RMSE: 0.3778, 验证MAE: 0.2759
2025-05-30 23:30:54,182 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.2368, LR: 0.002709
2025-05-30 23:31:00,887 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.3275, LR: 0.002608
2025-05-30 23:31:01,624 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:31:02,048 - models.mobilenet_model - INFO - Epoch 25/50:
2025-05-30 23:31:02,048 - models.mobilenet_model - INFO -   学习率: 0.002719
2025-05-30 23:31:02,049 - models.mobilenet_model - INFO -   训练损失: 0.2598, 训练R²: -3.3395
2025-05-30 23:31:02,049 - models.mobilenet_model - INFO -   验证损失: 0.0515, 验证R²: -0.3861
2025-05-30 23:31:02,049 - models.mobilenet_model - INFO -   验证RMSE: 0.3692, 验证MAE: 0.2725
2025-05-30 23:31:02,736 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.3254, LR: 0.002587
2025-05-30 23:31:09,442 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.2007, LR: 0.002484
2025-05-30 23:31:10,173 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:31:10,597 - models.mobilenet_model - INFO - Epoch 26/50:
2025-05-30 23:31:10,598 - models.mobilenet_model - INFO -   学习率: 0.002598
2025-05-30 23:31:10,598 - models.mobilenet_model - INFO -   训练损失: 0.2462, 训练R²: -3.3711
2025-05-30 23:31:10,598 - models.mobilenet_model - INFO -   验证损失: 0.0629, 验证R²: -0.8783
2025-05-30 23:31:10,598 - models.mobilenet_model - INFO -   验证RMSE: 0.4298, 验证MAE: 0.3040
2025-05-30 23:31:11,284 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.0761, LR: 0.002463
2025-05-30 23:31:17,984 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.2960, LR: 0.002358
2025-05-30 23:31:18,713 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:31:19,148 - models.mobilenet_model - INFO - Epoch 27/50:
2025-05-30 23:31:19,148 - models.mobilenet_model - INFO -   学习率: 0.002473
2025-05-30 23:31:19,149 - models.mobilenet_model - INFO -   训练损失: 0.2124, 训练R²: -1.7708
2025-05-30 23:31:19,149 - models.mobilenet_model - INFO -   验证损失: 0.0662, 验证R²: -0.8424
2025-05-30 23:31:19,149 - models.mobilenet_model - INFO -   验证RMSE: 0.4256, 验证MAE: 0.3142
2025-05-30 23:31:19,833 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.0568, LR: 0.002337
2025-05-30 23:31:26,536 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.1782, LR: 0.002230
2025-05-30 23:31:27,258 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:31:27,695 - models.mobilenet_model - INFO - Epoch 28/50:
2025-05-30 23:31:27,695 - models.mobilenet_model - INFO -   学习率: 0.002347
2025-05-30 23:31:27,695 - models.mobilenet_model - INFO -   训练损失: 0.1786, 训练R²: -1.6606
2025-05-30 23:31:27,696 - models.mobilenet_model - INFO -   验证损失: 0.0908, 验证R²: -1.3811
2025-05-30 23:31:27,696 - models.mobilenet_model - INFO -   验证RMSE: 0.4839, 验证MAE: 0.3875
2025-05-30 23:31:28,382 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.1507, LR: 0.002209
2025-05-30 23:31:35,090 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.1904, LR: 0.002102
2025-05-30 23:31:35,817 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:31:36,243 - models.mobilenet_model - INFO - Epoch 29/50:
2025-05-30 23:31:36,243 - models.mobilenet_model - INFO -   学习率: 0.002220
2025-05-30 23:31:36,244 - models.mobilenet_model - INFO -   训练损失: 0.1769, 训练R²: -1.1953
2025-05-30 23:31:36,244 - models.mobilenet_model - INFO -   验证损失: 0.0984, 验证R²: -1.8292
2025-05-30 23:31:36,244 - models.mobilenet_model - INFO -   验证RMSE: 0.5275, 验证MAE: 0.4064
2025-05-30 23:31:36,935 - models.mobilenet_model - INFO - 批次 0/13, 损失: 0.2070, LR: 0.002081
2025-05-30 23:31:43,637 - models.mobilenet_model - INFO - 批次 10/13, 损失: 0.2743, LR: 0.001973
2025-05-30 23:31:44,366 - models.mobilenet_model - WARNING - 训练批次 12 失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 512])
2025-05-30 23:31:44,796 - models.mobilenet_model - INFO - Epoch 30/50:
2025-05-30 23:31:44,796 - models.mobilenet_model - INFO -   学习率: 0.002091
2025-05-30 23:31:44,797 - models.mobilenet_model - INFO -   训练损失: 0.2092, 训练R²: -2.1378
2025-05-30 23:31:44,797 - models.mobilenet_model - INFO -   验证损失: 0.1094, 验证R²: -2.5227
2025-05-30 23:31:44,797 - models.mobilenet_model - INFO -   验证RMSE: 0.5886, 验证MAE: 0.3992
2025-05-30 23:31:44,798 - models.mobilenet_model - INFO - 早停: 验证损失在 15 个epoch内未改善
2025-05-30 23:31:44,798 - models.mobilenet_model - INFO - 训练完成! 最佳验证损失: 0.0362
2025-05-30 23:31:45,673 - models.mobilenet_model - INFO - === MobileNet模型测试结果 ===
2025-05-30 23:31:45,673 - models.mobilenet_model - INFO - 样本数量: 13
2025-05-30 23:31:45,673 - models.mobilenet_model - INFO - R² Score: 0.1590
2025-05-30 23:31:45,674 - models.mobilenet_model - INFO - RMSE: 0.5590
2025-05-30 23:31:45,674 - models.mobilenet_model - INFO - MAE: 0.4485
2025-05-30 23:31:45,674 - models.mobilenet_model - INFO - MAPE: 49.23%
2025-05-30 23:31:45,680 - models.mobilenet_model - INFO - 测试结果保存至: results/test_results/mobilenet_test_results.json
2025-05-30 23:31:45,686 - __main__ - INFO - MobileNet 训练完成，耗时: 4.36 分钟
2025-05-30 23:31:45,689 - __main__ - INFO - ✅ MobileNet 训练成功 (耗时: 4.4分钟)
2025-05-30 23:31:45,689 - __main__ - INFO - 等待5秒让GPU休息...
2025-05-30 23:31:50,697 - __main__ - INFO - 
================================================================================
2025-05-30 23:31:50,698 - __main__ - INFO - 进度: 2/8 - 开始训练 EfficientNet
2025-05-30 23:31:50,699 - __main__ - INFO - ================================================================================
2025-05-30 23:31:50,723 - __main__ - INFO - 
============================================================
2025-05-30 23:31:50,723 - __main__ - INFO - 开始训练 EfficientNet 模型
2025-05-30 23:31:50,723 - __main__ - INFO - ============================================================
2025-05-30 23:31:50,730 - models.efficientnet_model - INFO - 使用设备: cuda
2025-05-30 23:31:50,730 - models.efficientnet_model - INFO - GPU显存: 4.0 GB
2025-05-30 23:31:59,343 - models.efficientnet_model - ERROR - 主程序执行失败: invalid hash value (expected "3dd342df", got "7f5810bc96def8f7552d5b7e68d53c4786f81167d28291b21c0d90e1fca14934")
2025-05-30 23:31:59,351 - models.efficientnet_model - ERROR - Traceback (most recent call last):
  File "D:\研二\能耗估算\666-模型对比项目\V8\models\efficientnet_model.py", line 554, in main
    model = ImprovedEfficientNetFeatureExtractor(
  File "D:\研二\能耗估算\666-模型对比项目\V8\models\efficientnet_model.py", line 64, in __init__
    self.backbone = models.efficientnet_b0(pretrained=pretrained)
  File "D:\anaconda3\envs\ty\lib\site-packages\torchvision\models\_utils.py", line 142, in wrapper
    return fn(*args, **kwargs)
  File "D:\anaconda3\envs\ty\lib\site-packages\torchvision\models\_utils.py", line 228, in inner_wrapper
    return builder(*args, **kwargs)
  File "D:\anaconda3\envs\ty\lib\site-packages\torchvision\models\efficientnet.py", line 770, in efficientnet_b0
    return _efficientnet(
  File "D:\anaconda3\envs\ty\lib\site-packages\torchvision\models\efficientnet.py", line 360, in _efficientnet
    model.load_state_dict(weights.get_state_dict(progress=progress, check_hash=True))
  File "D:\anaconda3\envs\ty\lib\site-packages\torchvision\models\_api.py", line 90, in get_state_dict
    return load_state_dict_from_url(self.url, *args, **kwargs)
  File "D:\anaconda3\envs\ty\lib\site-packages\torch\hub.py", line 766, in load_state_dict_from_url
    download_url_to_file(url, cached_file, hash_prefix, progress=progress)
  File "D:\anaconda3\envs\ty\lib\site-packages\torch\hub.py", line 663, in download_url_to_file
    raise RuntimeError(f'invalid hash value (expected "{hash_prefix}", got "{digest}")')
RuntimeError: invalid hash value (expected "3dd342df", got "7f5810bc96def8f7552d5b7e68d53c4786f81167d28291b21c0d90e1fca14934")

2025-05-30 23:31:59,357 - __main__ - INFO - EfficientNet 训练完成，耗时: 0.14 分钟
2025-05-30 23:31:59,358 - __main__ - INFO - ✅ EfficientNet 训练成功 (耗时: 0.1分钟)
2025-05-30 23:31:59,358 - __main__ - INFO - 等待5秒让GPU休息...
2025-05-30 23:32:04,364 - __main__ - INFO - 
================================================================================
2025-05-30 23:32:04,365 - __main__ - INFO - 进度: 3/8 - 开始训练 ConvNeXt
2025-05-30 23:32:04,366 - __main__ - INFO - ================================================================================
2025-05-30 23:32:04,367 - __main__ - INFO - 
============================================================
2025-05-30 23:32:04,368 - __main__ - INFO - 开始训练 ConvNeXt 模型
2025-05-30 23:32:04,368 - __main__ - INFO - ============================================================
2025-05-30 23:32:05,013 - __main__ - ERROR - ConvNeXt 训练失败: No module named 'improved_data_loader'
2025-05-30 23:32:05,015 - __main__ - ERROR - Traceback (most recent call last):
  File "main_runner.py", line 88, in train_single_model
    convnext_main()
  File "D:\研二\能耗估算\666-模型对比项目\V8\models\convnext_model.py", line 276, in main
    from improved_data_loader import create_data_loaders, CONFIGS
ModuleNotFoundError: No module named 'improved_data_loader'

2025-05-30 23:32:05,015 - __main__ - ERROR - ❌ ConvNeXt 训练失败
2025-05-30 23:32:05,016 - __main__ - INFO - 等待5秒让GPU休息...
2025-05-30 23:32:10,022 - __main__ - INFO - 
================================================================================
2025-05-30 23:32:10,023 - __main__ - INFO - 进度: 4/8 - 开始训练 DenseNet
2025-05-30 23:32:10,024 - __main__ - INFO - ================================================================================
2025-05-30 23:32:10,025 - __main__ - INFO - 
============================================================
2025-05-30 23:32:10,027 - __main__ - INFO - 开始训练 DenseNet 模型
2025-05-30 23:32:10,028 - __main__ - INFO - ============================================================
2025-05-30 23:32:10,070 - __main__ - ERROR - DenseNet 训练失败: No module named 'improved_data_loader'
2025-05-30 23:32:10,072 - __main__ - ERROR - Traceback (most recent call last):
  File "main_runner.py", line 91, in train_single_model
    densenet_main()
  File "D:\研二\能耗估算\666-模型对比项目\V8\models\densenet_model.py", line 278, in main
    from improved_data_loader import create_data_loaders, CONFIGS
ModuleNotFoundError: No module named 'improved_data_loader'

2025-05-30 23:32:10,074 - __main__ - ERROR - ❌ DenseNet 训练失败
2025-05-30 23:32:10,074 - __main__ - INFO - 等待5秒让GPU休息...
2025-05-30 23:32:15,089 - __main__ - INFO - 
================================================================================
2025-05-30 23:32:15,091 - __main__ - INFO - 进度: 5/8 - 开始训练 ResNet
2025-05-30 23:32:15,093 - __main__ - INFO - ================================================================================
2025-05-30 23:32:15,094 - __main__ - INFO - 
============================================================
2025-05-30 23:32:15,096 - __main__ - INFO - 开始训练 ResNet 模型
2025-05-30 23:32:15,098 - __main__ - INFO - ============================================================
2025-05-30 23:32:15,126 - models.resnet_model - INFO - 使用设备: cuda
2025-05-30 23:32:15,127 - models.resnet_model - INFO - GPU显存: 4.0 GB
2025-05-30 23:32:15,128 - data_loader - INFO - 加载区域信息和能耗标签...
2025-05-30 23:32:15,145 - data_loader - INFO - train集包含 97 个区域
2025-05-30 23:32:15,148 - data_loader - INFO - 加载POI-街景图像映射...
2025-05-30 23:32:15,178 - data_loader - INFO - 成功构建 97 个有效样本
2025-05-30 23:32:15,179 - data_loader - INFO - 加载区域信息和能耗标签...
2025-05-30 23:32:15,191 - data_loader - INFO - val集包含 12 个区域
2025-05-30 23:32:15,191 - data_loader - INFO - 加载POI-街景图像映射...
2025-05-30 23:32:15,195 - data_loader - INFO - 成功构建 12 个有效样本
2025-05-30 23:32:15,196 - data_loader - INFO - 加载区域信息和能耗标签...
2025-05-30 23:32:15,208 - data_loader - INFO - test集包含 13 个区域
2025-05-30 23:32:15,208 - data_loader - INFO - 加载POI-街景图像映射...
2025-05-30 23:32:15,211 - data_loader - INFO - 成功构建 13 个有效样本
2025-05-30 23:32:15,211 - data_loader - INFO - 数据加载器创建完成:
2025-05-30 23:32:15,211 - data_loader - INFO -   训练集: 97 样本
2025-05-30 23:32:15,215 - data_loader - INFO -   验证集: 12 样本
2025-05-30 23:32:15,216 - data_loader - INFO -   测试集: 13 样本
2025-05-30 23:32:15,626 - models.resnet_model - INFO - 已冻结ResNet早期层
2025-05-30 23:32:15,663 - models.resnet_model - INFO - 模型参数总数: 26,210,241
2025-05-30 23:32:15,663 - models.resnet_model - INFO - 可训练参数: 25,984,897
2025-05-30 23:32:16,083 - models.resnet_model - INFO - 开始训练ResNet模型，总共 50 个epoch
2025-05-30 23:32:16,083 - models.resnet_model - INFO - 预热期: 5 epochs
2025-05-30 23:32:16,793 - models.resnet_model - INFO - 训练批次 0/17, 损失: 1.5017
2025-05-30 23:32:21,832 - models.resnet_model - INFO - 训练批次 10/17, 损失: 1.0552
2025-05-30 23:32:25,044 - models.resnet_model - INFO - Epoch 1/50:
2025-05-30 23:32:25,044 - models.resnet_model - INFO -   学习率: 0.000100
2025-05-30 23:32:25,045 - models.resnet_model - INFO -   训练损失: 1.2426, 训练R²: -13.3426
2025-05-30 23:32:25,045 - models.resnet_model - INFO -   验证损失: 0.6399, 验证R²: -13.0006
2025-05-30 23:32:25,045 - models.resnet_model - INFO -   验证RMSE: 1.1734, 验证MAE: 1.1202
2025-05-30 23:32:25,328 - models.resnet_model - INFO - 保存最佳模型: ./results/saved_models\shenyang_ResNet_improved.pt
2025-05-30 23:32:25,867 - models.resnet_model - INFO - 训练批次 0/17, 损失: 1.4056
2025-05-30 23:32:30,921 - models.resnet_model - INFO - 训练批次 10/17, 损失: 1.0135
2025-05-30 23:32:34,111 - models.resnet_model - INFO - Epoch 2/50:
2025-05-30 23:32:34,111 - models.resnet_model - INFO -   学习率: 0.000100
2025-05-30 23:32:34,111 - models.resnet_model - INFO -   训练损失: 0.8094, 训练R²: -7.2692
2025-05-30 23:32:34,111 - models.resnet_model - INFO -   验证损失: 0.1144, 验证R²: -1.3263
2025-05-30 23:32:34,111 - models.resnet_model - INFO -   验证RMSE: 0.4783, 验证MAE: 0.4048
2025-05-30 23:32:34,427 - models.resnet_model - INFO - 保存最佳模型: ./results/saved_models\shenyang_ResNet_improved.pt
2025-05-30 23:32:34,958 - models.resnet_model - INFO - 训练批次 0/17, 损失: 0.6260
2025-05-30 23:32:40,078 - models.resnet_model - INFO - 训练批次 10/17, 损失: 0.1389
2025-05-30 23:32:43,565 - models.resnet_model - INFO - Epoch 3/50:
2025-05-30 23:32:43,568 - models.resnet_model - INFO -   学习率: 0.000100
2025-05-30 23:32:43,568 - models.resnet_model - INFO -   训练损失: 0.3591, 训练R²: -1.8539
2025-05-30 23:32:43,568 - models.resnet_model - INFO -   验证损失: 0.0573, 验证R²: -0.1655
2025-05-30 23:32:43,568 - models.resnet_model - INFO -   验证RMSE: 0.3385, 验证MAE: 0.2785
2025-05-30 23:32:43,906 - models.resnet_model - INFO - 保存最佳模型: ./results/saved_models\shenyang_ResNet_improved.pt
2025-05-30 23:32:44,549 - models.resnet_model - INFO - 训练批次 0/17, 损失: 0.1025
2025-05-30 23:32:50,439 - models.resnet_model - INFO - 训练批次 10/17, 损失: 0.2516
2025-05-30 23:32:54,025 - models.resnet_model - INFO - Epoch 4/50:
2025-05-30 23:32:54,025 - models.resnet_model - INFO -   学习率: 0.000100
2025-05-30 23:32:54,025 - models.resnet_model - INFO -   训练损失: 0.2214, 训练R²: -0.9317
2025-05-30 23:32:54,026 - models.resnet_model - INFO -   验证损失: 0.0882, 验证R²: -0.7941
2025-05-30 23:32:54,026 - models.resnet_model - INFO -   验证RMSE: 0.4200, 验证MAE: 0.3617
2025-05-30 23:32:54,648 - models.resnet_model - INFO - 训练批次 0/17, 损失: 0.1088
2025-05-30 23:33:00,856 - models.resnet_model - INFO - 训练批次 10/17, 损失: 0.2549
2025-05-30 23:33:04,239 - models.resnet_model - INFO - Epoch 5/50:
2025-05-30 23:33:04,240 - models.resnet_model - INFO -   学习率: 0.000100
2025-05-30 23:33:04,240 - models.resnet_model - INFO -   训练损失: 0.1974, 训练R²: -0.6550
2025-05-30 23:33:04,240 - models.resnet_model - INFO -   验证损失: 0.0912, 验证R²: -0.8548
2025-05-30 23:33:04,241 - models.resnet_model - INFO -   验证RMSE: 0.4271, 验证MAE: 0.3710
2025-05-30 23:33:04,242 - models.resnet_model - INFO - 已解冻所有层
2025-05-30 23:33:04,242 - models.resnet_model - INFO - Epoch 5: 解冻所有层进行微调
2025-05-30 23:33:04,836 - models.resnet_model - INFO - 训练批次 0/17, 损失: 0.2274
2025-05-30 23:33:11,310 - models.resnet_model - INFO - 训练批次 10/17, 损失: 0.1332
2025-05-30 23:33:15,179 - models.resnet_model - INFO - Epoch 6/50:
2025-05-30 23:33:15,179 - models.resnet_model - INFO -   学习率: 0.000100
2025-05-30 23:33:15,180 - models.resnet_model - INFO -   训练损失: 0.1962, 训练R²: -0.4935
2025-05-30 23:33:15,180 - models.resnet_model - INFO -   验证损失: 0.0851, 验证R²: -0.7311
2025-05-30 23:33:15,180 - models.resnet_model - INFO -   验证RMSE: 0.4126, 验证MAE: 0.3592
2025-05-30 23:33:15,843 - models.resnet_model - INFO - 训练批次 0/17, 损失: 0.3174
2025-05-30 23:33:22,552 - models.resnet_model - INFO - 训练批次 10/17, 损失: 0.1159
2025-05-30 23:33:26,547 - models.resnet_model - INFO - Epoch 7/50:
2025-05-30 23:33:26,547 - models.resnet_model - INFO -   学习率: 0.000001
2025-05-30 23:33:26,547 - models.resnet_model - INFO -   训练损失: 0.1802, 训练R²: -0.4723
2025-05-30 23:33:26,548 - models.resnet_model - INFO -   验证损失: 0.0973, 验证R²: -0.9795
2025-05-30 23:33:26,548 - models.resnet_model - INFO -   验证RMSE: 0.4412, 验证MAE: 0.3848
2025-05-30 23:33:27,113 - models.resnet_model - INFO - 训练批次 0/17, 损失: 0.0809
2025-05-30 23:33:33,455 - models.resnet_model - INFO - 训练批次 10/17, 损失: 0.1395
2025-05-30 23:33:37,291 - models.resnet_model - INFO - Epoch 8/50:
2025-05-30 23:33:37,291 - models.resnet_model - INFO -   学习率: 0.000001
2025-05-30 23:33:37,292 - models.resnet_model - INFO -   训练损失: 0.2012, 训练R²: -0.5684
2025-05-30 23:33:37,292 - models.resnet_model - INFO -   验证损失: 0.0827, 验证R²: -0.6818
2025-05-30 23:33:37,293 - models.resnet_model - INFO -   验证RMSE: 0.4067, 验证MAE: 0.3496
2025-05-30 23:33:37,912 - models.resnet_model - INFO - 训练批次 0/17, 损失: 0.1068
2025-05-30 23:33:44,361 - models.resnet_model - INFO - 训练批次 10/17, 损失: 0.2580
2025-05-30 23:33:48,360 - models.resnet_model - INFO - Epoch 9/50:
2025-05-30 23:33:48,361 - models.resnet_model - INFO -   学习率: 0.000001
2025-05-30 23:33:48,361 - models.resnet_model - INFO -   训练损失: 0.1712, 训练R²: -0.4352
2025-05-30 23:33:48,361 - models.resnet_model - INFO -   验证损失: 0.0913, 验证R²: -0.8567
2025-05-30 23:33:48,361 - models.resnet_model - INFO -   验证RMSE: 0.4273, 验证MAE: 0.3537
2025-05-30 23:33:48,949 - models.resnet_model - INFO - 训练批次 0/17, 损失: 0.2143
2025-05-30 23:33:55,432 - models.resnet_model - INFO - 训练批次 10/17, 损失: 0.2010
2025-05-30 23:33:59,438 - models.resnet_model - INFO - Epoch 10/50:
2025-05-30 23:33:59,439 - models.resnet_model - INFO -   学习率: 0.000001
2025-05-30 23:33:59,439 - models.resnet_model - INFO -   训练损失: 0.1901, 训练R²: -0.5514
2025-05-30 23:33:59,440 - models.resnet_model - INFO -   验证损失: 0.0941, 验证R²: -0.9140
2025-05-30 23:33:59,440 - models.resnet_model - INFO -   验证RMSE: 0.4338, 验证MAE: 0.3754
2025-05-30 23:34:00,044 - models.resnet_model - INFO - 训练批次 0/17, 损失: 0.1512
2025-05-30 23:34:06,344 - models.resnet_model - INFO - 训练批次 10/17, 损失: 0.1385
2025-05-30 23:34:09,869 - models.resnet_model - INFO - Epoch 11/50:
2025-05-30 23:34:09,870 - models.resnet_model - INFO -   学习率: 0.000001
2025-05-30 23:34:09,871 - models.resnet_model - INFO -   训练损失: 0.2065, 训练R²: -0.6622
2025-05-30 23:34:09,871 - models.resnet_model - INFO -   验证损失: 0.0906, 验证R²: -0.8429
2025-05-30 23:34:09,871 - models.resnet_model - INFO -   验证RMSE: 0.4257, 验证MAE: 0.3729
2025-05-30 23:34:10,541 - models.resnet_model - INFO - 训练批次 0/17, 损失: 0.4297
2025-05-30 23:34:17,245 - models.resnet_model - INFO - 训练批次 10/17, 损失: 0.2008
2025-05-30 23:34:21,078 - models.resnet_model - INFO - Epoch 12/50:
2025-05-30 23:34:21,079 - models.resnet_model - INFO -   学习率: 0.000001
2025-05-30 23:34:21,079 - models.resnet_model - INFO -   训练损失: 0.1853, 训练R²: -0.5003
2025-05-30 23:34:21,080 - models.resnet_model - INFO -   验证损失: 0.0985, 验证R²: -1.0040
2025-05-30 23:34:21,081 - models.resnet_model - INFO -   验证RMSE: 0.4439, 验证MAE: 0.3817
2025-05-30 23:34:21,780 - models.resnet_model - INFO - 训练批次 0/17, 损失: 0.3285
2025-05-30 23:34:28,147 - models.resnet_model - INFO - 训练批次 10/17, 损失: 0.1709
2025-05-30 23:34:31,990 - models.resnet_model - INFO - Epoch 13/50:
2025-05-30 23:34:31,991 - models.resnet_model - INFO -   学习率: 0.000001
2025-05-30 23:34:31,991 - models.resnet_model - INFO -   训练损失: 0.2044, 训练R²: -0.5889
2025-05-30 23:34:31,992 - models.resnet_model - INFO -   验证损失: 0.1004, 验证R²: -1.0411
2025-05-30 23:34:31,992 - models.resnet_model - INFO -   验证RMSE: 0.4480, 验证MAE: 0.3880
2025-05-30 23:34:32,573 - models.resnet_model - INFO - 训练批次 0/17, 损失: 0.1409
2025-05-30 23:34:38,927 - models.resnet_model - INFO - 训练批次 10/17, 损失: 0.0893
2025-05-30 23:34:42,573 - models.resnet_model - INFO - Epoch 14/50:
2025-05-30 23:34:42,573 - models.resnet_model - INFO -   学习率: 0.000001
2025-05-30 23:34:42,574 - models.resnet_model - INFO -   训练损失: 0.1907, 训练R²: -0.4761
2025-05-30 23:34:42,574 - models.resnet_model - INFO -   验证损失: 0.0987, 验证R²: -1.0075
2025-05-30 23:34:42,574 - models.resnet_model - INFO -   验证RMSE: 0.4443, 验证MAE: 0.3845
2025-05-30 23:34:43,154 - models.resnet_model - INFO - 训练批次 0/17, 损失: 0.1046
2025-05-30 23:34:49,808 - models.resnet_model - INFO - 训练批次 10/17, 损失: 0.1079
2025-05-30 23:34:53,814 - models.resnet_model - INFO - Epoch 15/50:
2025-05-30 23:34:53,814 - models.resnet_model - INFO -   学习率: 0.000001
2025-05-30 23:34:53,815 - models.resnet_model - INFO -   训练损失: 0.2041, 训练R²: -0.6897
2025-05-30 23:34:53,815 - models.resnet_model - INFO -   验证损失: 0.0947, 验证R²: -0.9260
2025-05-30 23:34:53,815 - models.resnet_model - INFO -   验证RMSE: 0.4352, 验证MAE: 0.3764
2025-05-30 23:34:54,381 - models.resnet_model - INFO - 训练批次 0/17, 损失: 0.1161
2025-05-30 23:35:00,883 - models.resnet_model - INFO - 训练批次 10/17, 损失: 0.1103
2025-05-30 23:35:04,889 - models.resnet_model - INFO - Epoch 16/50:
2025-05-30 23:35:04,890 - models.resnet_model - INFO -   学习率: 0.000001
2025-05-30 23:35:04,890 - models.resnet_model - INFO -   训练损失: 0.2140, 训练R²: -0.4176
2025-05-30 23:35:04,890 - models.resnet_model - INFO -   验证损失: 0.0966, 验证R²: -0.9639
2025-05-30 23:35:04,891 - models.resnet_model - INFO -   验证RMSE: 0.4395, 验证MAE: 0.3758
2025-05-30 23:35:05,470 - models.resnet_model - INFO - 训练批次 0/17, 损失: 0.3085
2025-05-30 23:35:11,784 - models.resnet_model - INFO - 训练批次 10/17, 损失: 0.0821
2025-05-30 23:35:15,304 - models.resnet_model - INFO - Epoch 17/50:
2025-05-30 23:35:15,305 - models.resnet_model - INFO -   学习率: 0.000001
2025-05-30 23:35:15,305 - models.resnet_model - INFO -   训练损失: 0.1637, 训练R²: -0.3439
2025-05-30 23:35:15,306 - models.resnet_model - INFO -   验证损失: 0.0919, 验证R²: -0.8683
2025-05-30 23:35:15,306 - models.resnet_model - INFO -   验证RMSE: 0.4286, 验证MAE: 0.3723
2025-05-30 23:35:15,983 - models.resnet_model - INFO - 训练批次 0/17, 损失: 0.0791
2025-05-30 23:35:22,694 - models.resnet_model - INFO - 训练批次 10/17, 损失: 0.2651
2025-05-30 23:35:26,520 - models.resnet_model - INFO - Epoch 18/50:
2025-05-30 23:35:26,521 - models.resnet_model - INFO -   学习率: 0.000001
2025-05-30 23:35:26,521 - models.resnet_model - INFO -   训练损失: 0.1907, 训练R²: -0.5578
2025-05-30 23:35:26,522 - models.resnet_model - INFO -   验证损失: 0.0989, 验证R²: -1.0106
2025-05-30 23:35:26,522 - models.resnet_model - INFO -   验证RMSE: 0.4447, 验证MAE: 0.3842
2025-05-30 23:35:27,218 - models.resnet_model - INFO - 训练批次 0/17, 损失: 0.0345
2025-05-30 23:35:33,608 - models.resnet_model - INFO - 训练批次 10/17, 损失: 0.0997
2025-05-30 23:35:37,437 - models.resnet_model - INFO - Epoch 19/50:
2025-05-30 23:35:37,438 - models.resnet_model - INFO -   学习率: 0.000001
2025-05-30 23:35:37,438 - models.resnet_model - INFO -   训练损失: 0.1792, 训练R²: -0.4614
2025-05-30 23:35:37,438 - models.resnet_model - INFO -   验证损失: 0.0883, 验证R²: -0.7967
2025-05-30 23:35:37,438 - models.resnet_model - INFO -   验证RMSE: 0.4203, 验证MAE: 0.3657
2025-05-30 23:35:38,008 - models.resnet_model - INFO - 训练批次 0/17, 损失: 0.1979
2025-05-30 23:35:44,348 - models.resnet_model - INFO - 训练批次 10/17, 损失: 0.1893
2025-05-30 23:35:48,216 - models.resnet_model - INFO - Epoch 20/50:
2025-05-30 23:35:48,216 - models.resnet_model - INFO -   学习率: 0.000001
2025-05-30 23:35:48,217 - models.resnet_model - INFO -   训练损失: 0.2105, 训练R²: -0.6214
2025-05-30 23:35:48,217 - models.resnet_model - INFO -   验证损失: 0.1020, 验证R²: -1.0751
2025-05-30 23:35:48,218 - models.resnet_model - INFO -   验证RMSE: 0.4517, 验证MAE: 0.3938
2025-05-30 23:35:48,880 - models.resnet_model - INFO - 训练批次 0/17, 损失: 0.1687
2025-05-30 23:35:55,583 - models.resnet_model - INFO - 训练批次 10/17, 损失: 0.3658
2025-05-30 23:35:59,584 - models.resnet_model - INFO - Epoch 21/50:
2025-05-30 23:35:59,585 - models.resnet_model - INFO -   学习率: 0.000001
2025-05-30 23:35:59,585 - models.resnet_model - INFO -   训练损失: 0.1879, 训练R²: -0.5326
2025-05-30 23:35:59,586 - models.resnet_model - INFO -   验证损失: 0.0823, 验证R²: -0.6734
2025-05-30 23:35:59,586 - models.resnet_model - INFO -   验证RMSE: 0.4057, 验证MAE: 0.3499
2025-05-30 23:36:00,161 - models.resnet_model - INFO - 训练批次 0/17, 损失: 0.3051
2025-05-30 23:36:06,535 - models.resnet_model - INFO - 训练批次 10/17, 损失: 0.3222
2025-05-30 23:36:10,491 - models.resnet_model - INFO - Epoch 22/50:
2025-05-30 23:36:10,492 - models.resnet_model - INFO -   学习率: 0.000001
2025-05-30 23:36:10,492 - models.resnet_model - INFO -   训练损失: 0.2248, 训练R²: -0.8864
2025-05-30 23:36:10,493 - models.resnet_model - INFO -   验证损失: 0.0800, 验证R²: -0.6265
2025-05-30 23:36:10,494 - models.resnet_model - INFO -   验证RMSE: 0.3999, 验证MAE: 0.3417
2025-05-30 23:36:11,070 - models.resnet_model - INFO - 训练批次 0/17, 损失: 0.1170
2025-05-30 23:36:17,555 - models.resnet_model - INFO - 训练批次 10/17, 损失: 0.2098
2025-05-30 23:36:21,396 - models.resnet_model - INFO - Epoch 23/50:
2025-05-30 23:36:21,396 - models.resnet_model - INFO -   学习率: 0.000001
2025-05-30 23:36:21,398 - models.resnet_model - INFO -   训练损失: 0.1678, 训练R²: -0.3735
2025-05-30 23:36:21,398 - models.resnet_model - INFO -   验证损失: 0.0915, 验证R²: -0.8610
2025-05-30 23:36:21,398 - models.resnet_model - INFO -   验证RMSE: 0.4278, 验证MAE: 0.3661
2025-05-30 23:36:21,398 - models.resnet_model - INFO - 早停: 验证损失在 20 个epoch内未改善
2025-05-30 23:36:21,398 - models.resnet_model - INFO - 训练完成! 最佳验证损失: 0.0573
2025-05-30 23:36:28,092 - models.resnet_model - INFO - ResNet改进版训练历史图保存至: resnet_improved_training_history.png
2025-05-30 23:36:28,872 - models.resnet_model - INFO - 已冻结ResNet早期层
2025-05-30 23:36:29,380 - models.resnet_model - INFO - === ResNet改进版模型测试结果 ===
2025-05-30 23:36:29,380 - models.resnet_model - INFO - R² Score: -0.6016
2025-05-30 23:36:29,381 - models.resnet_model - INFO - RMSE: 0.7714
2025-05-30 23:36:29,381 - models.resnet_model - INFO - MAE: 0.5774
2025-05-30 23:36:29,381 - models.resnet_model - INFO - MAPE: 65.98%
2025-05-30 23:36:29,385 - __main__ - ERROR - ResNet 训练失败: Object of type float32 is not JSON serializable
2025-05-30 23:36:29,388 - __main__ - ERROR - Traceback (most recent call last):
  File "main_runner.py", line 82, in train_single_model
    resnet_main()
  File "D:\研二\能耗估算\666-模型对比项目\V8\models\resnet_model.py", line 569, in main
    json.dump(json_results, f, indent=2)
  File "D:\anaconda3\envs\ty\lib\json\__init__.py", line 179, in dump
    for chunk in iterable:
  File "D:\anaconda3\envs\ty\lib\json\encoder.py", line 431, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "D:\anaconda3\envs\ty\lib\json\encoder.py", line 405, in _iterencode_dict
    yield from chunks
  File "D:\anaconda3\envs\ty\lib\json\encoder.py", line 438, in _iterencode
    o = _default(o)
  File "D:\anaconda3\envs\ty\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type float32 is not JSON serializable

2025-05-30 23:36:29,391 - __main__ - ERROR - ❌ ResNet 训练失败
2025-05-30 23:36:29,391 - __main__ - INFO - 等待5秒让GPU休息...
2025-05-30 23:36:34,394 - __main__ - INFO - 
================================================================================
2025-05-30 23:36:34,395 - __main__ - INFO - 进度: 6/8 - 开始训练 ViT
2025-05-30 23:36:34,395 - __main__ - INFO - ================================================================================
2025-05-30 23:36:34,416 - __main__ - INFO - 
============================================================
2025-05-30 23:36:34,417 - __main__ - INFO - 开始训练 ViT 模型
2025-05-30 23:36:34,417 - __main__ - INFO - ============================================================
2025-05-30 23:36:34,437 - models.vit_model - INFO - 使用设备: cuda
2025-05-30 23:36:34,437 - models.vit_model - INFO - GPU显存: 4.0 GB
2025-05-30 23:36:34,438 - data_loader - INFO - 加载区域信息和能耗标签...
2025-05-30 23:36:34,543 - data_loader - INFO - train集包含 97 个区域
2025-05-30 23:36:34,543 - data_loader - INFO - 加载POI-街景图像映射...
2025-05-30 23:36:34,578 - data_loader - INFO - 成功构建 97 个有效样本
2025-05-30 23:36:34,578 - data_loader - INFO - 加载区域信息和能耗标签...
2025-05-30 23:36:34,590 - data_loader - INFO - val集包含 12 个区域
2025-05-30 23:36:34,591 - data_loader - INFO - 加载POI-街景图像映射...
2025-05-30 23:36:34,595 - data_loader - INFO - 成功构建 12 个有效样本
2025-05-30 23:36:34,596 - data_loader - INFO - 加载区域信息和能耗标签...
2025-05-30 23:36:34,608 - data_loader - INFO - test集包含 13 个区域
2025-05-30 23:36:34,608 - data_loader - INFO - 加载POI-街景图像映射...
2025-05-30 23:36:34,613 - data_loader - INFO - 成功构建 13 个有效样本
2025-05-30 23:36:34,614 - data_loader - INFO - 数据加载器创建完成:
2025-05-30 23:36:34,614 - data_loader - INFO -   训练集: 97 样本
2025-05-30 23:36:34,615 - data_loader - INFO -   验证集: 12 样本
2025-05-30 23:36:34,615 - data_loader - INFO -   测试集: 13 样本
2025-05-30 23:36:35,108 - models.vit_model - INFO - 模型参数总数: 21,938,369
2025-05-30 23:36:35,109 - models.vit_model - INFO - 可训练参数: 21,938,369
2025-05-30 23:36:35,109 - models.vit_model - INFO - 模型大小: 83.7 MB
2025-05-30 23:36:35,251 - models.vit_model - INFO - 开始训练ViT模型，总共 50 个epoch
2025-05-30 23:36:35,251 - models.vit_model - INFO - Warmup期: 10 epochs
2025-05-30 23:36:35,970 - models.vit_model - INFO - Epoch 1, 批次 0/17, 损失: 0.1834, 学习率: 0.000000
2025-05-30 23:36:42,163 - models.vit_model - INFO - Epoch 1, 批次 10/17, 损失: 0.4734, 学习率: 0.000000
2025-05-30 23:36:45,847 - models.vit_model - INFO - Epoch 1/50:
2025-05-30 23:36:45,848 - models.vit_model - INFO -   学习率: 0.000000
2025-05-30 23:36:45,848 - models.vit_model - INFO -   训练损失: 0.5889, 训练R²: -4.2213
2025-05-30 23:36:45,848 - models.vit_model - INFO -   验证损失: 0.6978, 验证R²: -14.4947
2025-05-30 23:36:45,849 - models.vit_model - INFO -   验证RMSE: 1.2344, 验证MAE: 1.1938
2025-05-30 23:36:46,302 - models.vit_model - INFO - 保存最佳模型: ./results/saved_models\shenyang_ViT_improved.pt
2025-05-30 23:36:47,030 - models.vit_model - INFO - Epoch 2, 批次 0/17, 损失: 0.8283, 学习率: 0.000000
2025-05-30 23:36:53,256 - models.vit_model - INFO - Epoch 2, 批次 10/17, 损失: 0.2740, 学习率: 0.000000
2025-05-30 23:36:57,102 - models.vit_model - INFO - Epoch 2/50:
2025-05-30 23:36:57,102 - models.vit_model - INFO -   学习率: 0.000000
2025-05-30 23:36:57,102 - models.vit_model - INFO -   训练损失: 0.5978, 训练R²: -4.2145
2025-05-30 23:36:57,102 - models.vit_model - INFO -   验证损失: 0.6978, 验证R²: -14.4947
2025-05-30 23:36:57,103 - models.vit_model - INFO -   验证RMSE: 1.2344, 验证MAE: 1.1938
2025-05-30 23:36:57,688 - models.vit_model - INFO - Epoch 3, 批次 0/17, 损失: 0.7675, 学习率: 0.000000
2025-05-30 23:37:04,206 - models.vit_model - INFO - Epoch 3, 批次 10/17, 损失: 0.5889, 学习率: 0.000000
2025-05-30 23:37:08,015 - models.vit_model - INFO - Epoch 3/50:
2025-05-30 23:37:08,016 - models.vit_model - INFO -   学习率: 0.000000
2025-05-30 23:37:08,016 - models.vit_model - INFO -   训练损失: 0.5726, 训练R²: -4.1951
2025-05-30 23:37:08,016 - models.vit_model - INFO -   验证损失: 0.6978, 验证R²: -14.4947
2025-05-30 23:37:08,017 - models.vit_model - INFO -   验证RMSE: 1.2344, 验证MAE: 1.1938
2025-05-30 23:37:08,588 - models.vit_model - INFO - Epoch 4, 批次 0/17, 损失: 0.3954, 学习率: 0.000000
2025-05-30 23:37:14,916 - models.vit_model - INFO - Epoch 4, 批次 10/17, 损失: 0.8223, 学习率: 0.000000
2025-05-30 23:37:18,764 - models.vit_model - INFO - Epoch 4/50:
2025-05-30 23:37:18,764 - models.vit_model - INFO -   学习率: 0.000000
2025-05-30 23:37:18,765 - models.vit_model - INFO -   训练损失: 0.5741, 训练R²: -4.2146
2025-05-30 23:37:18,765 - models.vit_model - INFO -   验证损失: 0.6978, 验证R²: -14.4947
2025-05-30 23:37:18,765 - models.vit_model - INFO -   验证RMSE: 1.2344, 验证MAE: 1.1938
2025-05-30 23:37:19,346 - models.vit_model - INFO - Epoch 5, 批次 0/17, 损失: 0.5936, 学习率: 0.000000
2025-05-30 23:37:25,363 - models.vit_model - INFO - Epoch 5, 批次 10/17, 损失: 0.8344, 学习率: 0.000000
2025-05-30 23:37:29,000 - models.vit_model - INFO - Epoch 5/50:
2025-05-30 23:37:29,001 - models.vit_model - INFO -   学习率: 0.000000
2025-05-30 23:37:29,001 - models.vit_model - INFO -   训练损失: 0.6200, 训练R²: -4.1964
2025-05-30 23:37:29,002 - models.vit_model - INFO -   验证损失: 0.6978, 验证R²: -14.4947
2025-05-30 23:37:29,002 - models.vit_model - INFO -   验证RMSE: 1.2344, 验证MAE: 1.1938
2025-05-30 23:37:29,560 - models.vit_model - INFO - Epoch 6, 批次 0/17, 损失: 0.5932, 学习率: 0.000000
2025-05-30 23:37:35,602 - models.vit_model - INFO - Epoch 6, 批次 10/17, 损失: 0.7061, 学习率: 0.000000
2025-05-30 23:37:39,289 - models.vit_model - INFO - Epoch 6/50:
2025-05-30 23:37:39,290 - models.vit_model - INFO -   学习率: 0.000000
2025-05-30 23:37:39,290 - models.vit_model - INFO -   训练损失: 0.5760, 训练R²: -4.2178
2025-05-30 23:37:39,290 - models.vit_model - INFO -   验证损失: 0.6978, 验证R²: -14.4947
2025-05-30 23:37:39,291 - models.vit_model - INFO -   验证RMSE: 1.2344, 验证MAE: 1.1938
2025-05-30 23:37:39,933 - models.vit_model - INFO - Epoch 7, 批次 0/17, 损失: 0.6392, 学习率: 0.000000
2025-05-30 23:37:46,142 - models.vit_model - INFO - Epoch 7, 批次 10/17, 损失: 0.5494, 学习率: 0.000000
2025-05-30 23:37:49,867 - models.vit_model - INFO - Epoch 7/50:
2025-05-30 23:37:49,867 - models.vit_model - INFO -   学习率: 0.000000
2025-05-30 23:37:49,867 - models.vit_model - INFO -   训练损失: 0.6063, 训练R²: -4.2152
2025-05-30 23:37:49,868 - models.vit_model - INFO -   验证损失: 0.6978, 验证R²: -14.4947
2025-05-30 23:37:49,868 - models.vit_model - INFO -   验证RMSE: 1.2344, 验证MAE: 1.1938
2025-05-30 23:37:50,514 - models.vit_model - INFO - Epoch 8, 批次 0/17, 损失: 0.5479, 学习率: 0.000000
2025-05-30 23:37:56,881 - models.vit_model - INFO - Epoch 8, 批次 10/17, 损失: 0.7590, 学习率: 0.000000
2025-05-30 23:38:00,565 - models.vit_model - INFO - Epoch 8/50:
2025-05-30 23:38:00,566 - models.vit_model - INFO -   学习率: 0.000000
2025-05-30 23:38:00,566 - models.vit_model - INFO -   训练损失: 0.6060, 训练R²: -4.1969
2025-05-30 23:38:00,566 - models.vit_model - INFO -   验证损失: 0.6978, 验证R²: -14.4947
2025-05-30 23:38:00,567 - models.vit_model - INFO -   验证RMSE: 1.2344, 验证MAE: 1.1938
2025-05-30 23:38:01,154 - models.vit_model - INFO - Epoch 9, 批次 0/17, 损失: 0.7503, 学习率: 0.000000
2025-05-30 23:38:07,465 - models.vit_model - INFO - Epoch 9, 批次 10/17, 损失: 0.7756, 学习率: 0.000000
2025-05-30 23:38:11,145 - models.vit_model - INFO - Epoch 9/50:
2025-05-30 23:38:11,145 - models.vit_model - INFO -   学习率: 0.000000
2025-05-30 23:38:11,146 - models.vit_model - INFO -   训练损失: 0.5918, 训练R²: -4.1908
2025-05-30 23:38:11,146 - models.vit_model - INFO -   验证损失: 0.6978, 验证R²: -14.4947
2025-05-30 23:38:11,146 - models.vit_model - INFO -   验证RMSE: 1.2344, 验证MAE: 1.1938
2025-05-30 23:38:11,750 - models.vit_model - INFO - Epoch 10, 批次 0/17, 损失: 0.5670, 学习率: 0.000000
2025-05-30 23:38:18,099 - models.vit_model - INFO - Epoch 10, 批次 10/17, 损失: 0.5417, 学习率: 0.000000
2025-05-30 23:38:21,890 - models.vit_model - INFO - Epoch 10/50:
2025-05-30 23:38:21,890 - models.vit_model - INFO -   学习率: 0.000000
2025-05-30 23:38:21,891 - models.vit_model - INFO -   训练损失: 0.5752, 训练R²: -4.2210
2025-05-30 23:38:21,891 - models.vit_model - INFO -   验证损失: 0.6978, 验证R²: -14.4947
2025-05-30 23:38:21,891 - models.vit_model - INFO -   验证RMSE: 1.2344, 验证MAE: 1.1938
2025-05-30 23:38:22,467 - models.vit_model - INFO - Epoch 11, 批次 0/17, 损失: 0.7928, 学习率: 0.000000
2025-05-30 23:38:28,622 - models.vit_model - INFO - Epoch 11, 批次 10/17, 损失: 0.6126, 学习率: 0.000000
2025-05-30 23:38:32,504 - models.vit_model - INFO - Epoch 11/50:
2025-05-30 23:38:32,505 - models.vit_model - INFO -   学习率: 0.000000
2025-05-30 23:38:32,505 - models.vit_model - INFO -   训练损失: 0.5989, 训练R²: -4.1989
2025-05-30 23:38:32,506 - models.vit_model - INFO -   验证损失: 0.6978, 验证R²: -14.4947
2025-05-30 23:38:32,506 - models.vit_model - INFO -   验证RMSE: 1.2344, 验证MAE: 1.1938
2025-05-30 23:38:33,158 - models.vit_model - INFO - Epoch 12, 批次 0/17, 损失: 0.3310, 学习率: 0.000000
2025-05-30 23:38:39,362 - models.vit_model - INFO - Epoch 12, 批次 10/17, 损失: 0.4995, 学习率: 0.000000
2025-05-30 23:38:43,214 - models.vit_model - INFO - Epoch 12/50:
2025-05-30 23:38:43,214 - models.vit_model - INFO -   学习率: 0.000000
2025-05-30 23:38:43,215 - models.vit_model - INFO -   训练损失: 0.5751, 训练R²: -4.2082
2025-05-30 23:38:43,215 - models.vit_model - INFO -   验证损失: 0.6978, 验证R²: -14.4947
2025-05-30 23:38:43,215 - models.vit_model - INFO -   验证RMSE: 1.2344, 验证MAE: 1.1938
2025-05-30 23:38:43,796 - models.vit_model - INFO - Epoch 13, 批次 0/17, 损失: 0.4922, 学习率: 0.000000
2025-05-30 23:38:49,940 - models.vit_model - INFO - Epoch 13, 批次 10/17, 损失: 0.5884, 学习率: 0.000000
2025-05-30 23:38:53,617 - models.vit_model - INFO - Epoch 13/50:
2025-05-30 23:38:53,617 - models.vit_model - INFO -   学习率: 0.000000
2025-05-30 23:38:53,617 - models.vit_model - INFO -   训练损失: 0.5729, 训练R²: -4.1985
2025-05-30 23:38:53,617 - models.vit_model - INFO -   验证损失: 0.6978, 验证R²: -14.4947
2025-05-30 23:38:53,618 - models.vit_model - INFO -   验证RMSE: 1.2344, 验证MAE: 1.1938
2025-05-30 23:38:54,185 - models.vit_model - INFO - Epoch 14, 批次 0/17, 损失: 0.6586, 学习率: 0.000000
2025-05-30 23:39:00,180 - models.vit_model - INFO - Epoch 14, 批次 10/17, 损失: 0.8344, 学习率: 0.000000
2025-05-30 23:39:03,718 - models.vit_model - INFO - Epoch 14/50:
2025-05-30 23:39:03,719 - models.vit_model - INFO -   学习率: 0.000000
2025-05-30 23:39:03,719 - models.vit_model - INFO -   训练损失: 0.6230, 训练R²: -4.2038
2025-05-30 23:39:03,720 - models.vit_model - INFO -   验证损失: 0.6978, 验证R²: -14.4947
2025-05-30 23:39:03,720 - models.vit_model - INFO -   验证RMSE: 1.2344, 验证MAE: 1.1938
2025-05-30 23:39:04,384 - models.vit_model - INFO - Epoch 15, 批次 0/17, 损失: 0.5187, 学习率: 0.000000
2025-05-30 23:39:10,766 - models.vit_model - INFO - Epoch 15, 批次 10/17, 损失: 0.7174, 学习率: 0.000000
2025-05-30 23:39:14,299 - models.vit_model - INFO - Epoch 15/50:
2025-05-30 23:39:14,299 - models.vit_model - INFO -   学习率: 0.000000
2025-05-30 23:39:14,300 - models.vit_model - INFO -   训练损失: 0.6012, 训练R²: -4.2121
2025-05-30 23:39:14,300 - models.vit_model - INFO -   验证损失: 0.6978, 验证R²: -14.4947
2025-05-30 23:39:14,300 - models.vit_model - INFO -   验证RMSE: 1.2344, 验证MAE: 1.1938
2025-05-30 23:39:14,958 - models.vit_model - INFO - Epoch 16, 批次 0/17, 损失: 0.6656, 学习率: 0.000000
2025-05-30 23:39:21,340 - models.vit_model - INFO - Epoch 16, 批次 10/17, 损失: 0.6943, 学习率: 0.000000
2025-05-30 23:39:25,185 - models.vit_model - INFO - Epoch 16/50:
2025-05-30 23:39:25,186 - models.vit_model - INFO -   学习率: 0.000000
2025-05-30 23:39:25,186 - models.vit_model - INFO -   训练损失: 0.5764, 训练R²: -4.2205
2025-05-30 23:39:25,187 - models.vit_model - INFO -   验证损失: 0.6978, 验证R²: -14.4947
2025-05-30 23:39:25,187 - models.vit_model - INFO -   验证RMSE: 1.2344, 验证MAE: 1.1938
2025-05-30 23:39:25,754 - models.vit_model - INFO - Epoch 17, 批次 0/17, 损失: 0.6478, 学习率: 0.000000
2025-05-30 23:39:31,910 - models.vit_model - INFO - Epoch 17, 批次 10/17, 损失: 0.5242, 学习率: 0.000000
2025-05-30 23:39:35,592 - models.vit_model - INFO - Epoch 17/50:
2025-05-30 23:39:35,593 - models.vit_model - INFO -   学习率: 0.000000
2025-05-30 23:39:35,593 - models.vit_model - INFO -   训练损失: 0.5738, 训练R²: -4.2122
2025-05-30 23:39:35,594 - models.vit_model - INFO -   验证损失: 0.6978, 验证R²: -14.4947
2025-05-30 23:39:35,594 - models.vit_model - INFO -   验证RMSE: 1.2344, 验证MAE: 1.1938
2025-05-30 23:39:36,179 - models.vit_model - INFO - Epoch 18, 批次 0/17, 损失: 0.6928, 学习率: 0.000000
2025-05-30 23:39:42,485 - models.vit_model - INFO - Epoch 18, 批次 10/17, 损失: 0.5654, 学习率: 0.000000
2025-05-30 23:39:46,172 - models.vit_model - INFO - Epoch 18/50:
2025-05-30 23:39:46,172 - models.vit_model - INFO -   学习率: 0.000000
2025-05-30 23:39:46,173 - models.vit_model - INFO -   训练损失: 0.5763, 训练R²: -4.2205
2025-05-30 23:39:46,173 - models.vit_model - INFO -   验证损失: 0.6978, 验证R²: -14.4947
2025-05-30 23:39:46,173 - models.vit_model - INFO -   验证RMSE: 1.2344, 验证MAE: 1.1938
2025-05-30 23:39:46,753 - models.vit_model - INFO - Epoch 19, 批次 0/17, 损失: 0.5844, 学习率: 0.000000
2025-05-30 23:39:53,067 - models.vit_model - INFO - Epoch 19, 批次 10/17, 损失: 0.4424, 学习率: 0.000000
2025-05-30 23:39:57,081 - models.vit_model - INFO - Epoch 19/50:
2025-05-30 23:39:57,081 - models.vit_model - INFO -   学习率: 0.000000
2025-05-30 23:39:57,081 - models.vit_model - INFO -   训练损失: 0.5942, 训练R²: -4.1927
2025-05-30 23:39:57,081 - models.vit_model - INFO -   验证损失: 0.6978, 验证R²: -14.4947
2025-05-30 23:39:57,082 - models.vit_model - INFO -   验证RMSE: 1.2344, 验证MAE: 1.1938
2025-05-30 23:39:57,756 - models.vit_model - INFO - Epoch 20, 批次 0/17, 损失: 0.3226, 学习率: 0.000000
2025-05-30 23:40:03,996 - models.vit_model - INFO - Epoch 20, 批次 10/17, 损失: 0.8921, 学习率: 0.000000
2025-05-30 23:40:07,818 - models.vit_model - INFO - Epoch 20/50:
2025-05-30 23:40:07,819 - models.vit_model - INFO -   学习率: 0.000000
2025-05-30 23:40:07,819 - models.vit_model - INFO -   训练损失: 0.5914, 训练R²: -4.2104
2025-05-30 23:40:07,819 - models.vit_model - INFO -   验证损失: 0.6978, 验证R²: -14.4947
2025-05-30 23:40:07,820 - models.vit_model - INFO -   验证RMSE: 1.2344, 验证MAE: 1.1938
2025-05-30 23:40:08,384 - models.vit_model - INFO - Epoch 21, 批次 0/17, 损失: 0.7076, 学习率: 0.000000
2025-05-30 23:40:14,720 - models.vit_model - INFO - Epoch 21, 批次 10/17, 损失: 0.3660, 学习率: 0.000000
2025-05-30 23:40:18,561 - models.vit_model - INFO - Epoch 21/50:
2025-05-30 23:40:18,561 - models.vit_model - INFO -   学习率: 0.000000
2025-05-30 23:40:18,562 - models.vit_model - INFO -   训练损失: 0.5877, 训练R²: -4.1967
2025-05-30 23:40:18,562 - models.vit_model - INFO -   验证损失: 0.6978, 验证R²: -14.4947
2025-05-30 23:40:18,562 - models.vit_model - INFO -   验证RMSE: 1.2344, 验证MAE: 1.1938
2025-05-30 23:40:18,563 - models.vit_model - INFO - 早停: 验证损失在 20 个epoch内未改善
2025-05-30 23:40:18,563 - models.vit_model - INFO - 训练完成! 最佳验证损失: 0.6978
2025-05-30 23:41:15,122 - models.vit_model - INFO - ViT改进版训练历史图保存至: vit_improved_training_history.png
2025-05-30 23:41:16,778 - models.vit_model - INFO - === ViT改进版模型测试结果 ===
2025-05-30 23:41:16,778 - models.vit_model - INFO - R² Score: -3.7640
2025-05-30 23:41:16,778 - models.vit_model - INFO - RMSE: 1.3304
2025-05-30 23:41:16,779 - models.vit_model - INFO - MAE: 1.1804
2025-05-30 23:41:16,779 - models.vit_model - INFO - MAPE: 99.61%
2025-05-30 23:41:16,790 - __main__ - ERROR - ViT 训练失败: Object of type float32 is not JSON serializable
2025-05-30 23:41:16,792 - __main__ - ERROR - Traceback (most recent call last):
  File "main_runner.py", line 85, in train_single_model
    vit_main()
  File "D:\研二\能耗估算\666-模型对比项目\V8\models\vit_model.py", line 787, in main
    json.dump(json_results, f, indent=2)
  File "D:\anaconda3\envs\ty\lib\json\__init__.py", line 179, in dump
    for chunk in iterable:
  File "D:\anaconda3\envs\ty\lib\json\encoder.py", line 431, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "D:\anaconda3\envs\ty\lib\json\encoder.py", line 405, in _iterencode_dict
    yield from chunks
  File "D:\anaconda3\envs\ty\lib\json\encoder.py", line 438, in _iterencode
    o = _default(o)
  File "D:\anaconda3\envs\ty\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type float32 is not JSON serializable

2025-05-30 23:41:16,797 - __main__ - ERROR - ❌ ViT 训练失败
2025-05-30 23:41:16,797 - __main__ - INFO - 等待5秒让GPU休息...
2025-05-30 23:41:21,809 - __main__ - INFO - 
================================================================================
2025-05-30 23:41:21,811 - __main__ - INFO - 进度: 7/8 - 开始训练 Swin
2025-05-30 23:41:21,812 - __main__ - INFO - ================================================================================
2025-05-30 23:41:21,894 - __main__ - INFO - 
============================================================
2025-05-30 23:41:21,895 - __main__ - INFO - 开始训练 Swin 模型
2025-05-30 23:41:21,896 - __main__ - INFO - ============================================================
2025-05-30 23:41:21,925 - __main__ - ERROR - Swin 训练失败: No module named 'improved_data_loader'
2025-05-30 23:41:21,926 - __main__ - ERROR - Traceback (most recent call last):
  File "main_runner.py", line 94, in train_single_model
    swin_main()
  File "D:\研二\能耗估算\666-模型对比项目\V8\models\swin_transformer_model.py", line 438, in main
    from improved_data_loader import create_data_loaders, CONFIGS
ModuleNotFoundError: No module named 'improved_data_loader'

2025-05-30 23:41:21,928 - __main__ - ERROR - ❌ Swin 训练失败
2025-05-30 23:41:21,928 - __main__ - INFO - 等待5秒让GPU休息...
2025-05-30 23:41:26,931 - __main__ - INFO - 
================================================================================
2025-05-30 23:41:26,933 - __main__ - INFO - 进度: 8/8 - 开始训练 MLP
2025-05-30 23:41:26,934 - __main__ - INFO - ================================================================================
2025-05-30 23:41:26,936 - __main__ - INFO - 
============================================================
2025-05-30 23:41:26,938 - __main__ - INFO - 开始训练 MLP 模型
2025-05-30 23:41:26,939 - __main__ - INFO - ============================================================
2025-05-30 23:41:26,988 - models.mlp_model - INFO - 使用设备: cuda
2025-05-30 23:41:26,989 - data_loader - INFO - 加载区域信息和能耗标签...
2025-05-30 23:41:27,016 - data_loader - INFO - train集包含 97 个区域
2025-05-30 23:41:27,016 - data_loader - INFO - 加载POI-街景图像映射...
2025-05-30 23:41:27,072 - data_loader - INFO - 成功构建 97 个有效样本
2025-05-30 23:41:27,073 - data_loader - INFO - 加载区域信息和能耗标签...
2025-05-30 23:41:27,084 - data_loader - INFO - val集包含 12 个区域
2025-05-30 23:41:27,084 - data_loader - INFO - 加载POI-街景图像映射...
2025-05-30 23:41:27,091 - data_loader - INFO - 成功构建 12 个有效样本
2025-05-30 23:41:27,092 - data_loader - INFO - 加载区域信息和能耗标签...
2025-05-30 23:41:27,104 - data_loader - INFO - test集包含 13 个区域
2025-05-30 23:41:27,105 - data_loader - INFO - 加载POI-街景图像映射...
2025-05-30 23:41:27,112 - data_loader - INFO - 成功构建 13 个有效样本
2025-05-30 23:41:27,113 - data_loader - INFO - 数据加载器创建完成:
2025-05-30 23:41:27,113 - data_loader - INFO -   训练集: 97 样本
2025-05-30 23:41:27,114 - data_loader - INFO -   验证集: 12 样本
2025-05-30 23:41:27,114 - data_loader - INFO -   测试集: 13 样本
2025-05-30 23:41:27,162 - models.mlp_model - INFO - 开始训练MLP模型，总共 50 个epoch
2025-05-30 23:41:28,325 - models.mlp_model - INFO - 训练批次 0/7, 损失: 2.0437
2025-05-30 23:41:34,416 - __main__ - ERROR - MLP 训练失败: Expected more than 1 value per channel when training, got input size torch.Size([1, 256])
2025-05-30 23:41:34,420 - __main__ - ERROR - Traceback (most recent call last):
  File "main_runner.py", line 73, in train_single_model
    mlp_main()
  File "D:\研二\能耗估算\666-模型对比项目\V8\models\mlp_model.py", line 383, in main
    best_model_path = trainer.train(
  File "D:\研二\能耗估算\666-模型对比项目\V8\models\mlp_model.py", line 219, in train
    train_loss, train_r2 = self.train_epoch(train_loader)
  File "D:\研二\能耗估算\666-模型对比项目\V8\models\mlp_model.py", line 166, in train_epoch
    outputs = self.model(images)
  File "D:\anaconda3\envs\ty\lib\site-packages\torch\nn\modules\module.py", line 1518, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\anaconda3\envs\ty\lib\site-packages\torch\nn\modules\module.py", line 1527, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\研二\能耗估算\666-模型对比项目\V8\models\mlp_model.py", line 125, in forward
    output = self.mlp(features)
  File "D:\anaconda3\envs\ty\lib\site-packages\torch\nn\modules\module.py", line 1518, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\anaconda3\envs\ty\lib\site-packages\torch\nn\modules\module.py", line 1527, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\研二\能耗估算\666-模型对比项目\V8\models\mlp_model.py", line 62, in forward
    return self.model(x)
  File "D:\anaconda3\envs\ty\lib\site-packages\torch\nn\modules\module.py", line 1518, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\anaconda3\envs\ty\lib\site-packages\torch\nn\modules\module.py", line 1527, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\anaconda3\envs\ty\lib\site-packages\torch\nn\modules\container.py", line 215, in forward
    input = module(input)
  File "D:\anaconda3\envs\ty\lib\site-packages\torch\nn\modules\module.py", line 1518, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\anaconda3\envs\ty\lib\site-packages\torch\nn\modules\module.py", line 1527, in _call_impl
    return forward_call(*args, **kwargs)
  File "D:\anaconda3\envs\ty\lib\site-packages\torch\nn\modules\batchnorm.py", line 171, in forward
    return F.batch_norm(
  File "D:\anaconda3\envs\ty\lib\site-packages\torch\nn\functional.py", line 2476, in batch_norm
    _verify_batch_size(input.size())
  File "D:\anaconda3\envs\ty\lib\site-packages\torch\nn\functional.py", line 2444, in _verify_batch_size
    raise ValueError(f"Expected more than 1 value per channel when training, got input size {size}")
ValueError: Expected more than 1 value per channel when training, got input size torch.Size([1, 256])

2025-05-30 23:41:34,427 - __main__ - ERROR - ❌ MLP 训练失败
2025-05-30 23:41:34,427 - __main__ - INFO - 
================================================================================
2025-05-30 23:41:34,428 - __main__ - INFO - 收集和分析结果...
2025-05-30 23:41:34,428 - __main__ - INFO - ================================================================================
2025-05-30 23:41:34,438 - __main__ - INFO - 
================================================================================
2025-05-30 23:41:34,438 - __main__ - INFO - 训练总结
2025-05-30 23:41:34,439 - __main__ - INFO - ================================================================================
2025-05-30 23:41:34,439 - __main__ - INFO - 总训练时间: 0.24 小时
2025-05-30 23:41:34,440 - __main__ - INFO - 成功训练的模型 (2): ['MobileNet', 'EfficientNet']
2025-05-30 23:41:34,441 - __main__ - INFO - 训练失败的模型 (6): ['ConvNeXt', 'DenseNet', 'ResNet', 'ViT', 'Swin', 'MLP']
2025-05-30 23:41:34,441 - __main__ - INFO - 
================================================================================
2025-05-30 23:41:34,442 - __main__ - INFO - 模型性能对比 (按R²分数排序)
2025-05-30 23:41:34,442 - __main__ - INFO - ================================================================================
2025-05-30 23:41:34,453 - __main__ - INFO - 
🏆 最佳模型: Mobilenet
2025-05-30 23:41:34,453 - __main__ - INFO -    R² Score: 0.1590
2025-05-30 23:41:34,453 - __main__ - INFO -    RMSE: 0.5590
2025-05-30 23:41:34,454 - __main__ - INFO -    MAE: 0.4485
2025-06-02 17:47:08,432 - __main__ - INFO - 训练单个模型: MLP
2025-06-02 17:47:08,433 - __main__ - INFO - 
============================================================
2025-06-02 17:47:08,433 - __main__ - INFO - 开始训练 MLP 模型
2025-06-02 17:47:08,433 - __main__ - INFO - ============================================================
2025-06-02 17:47:11,232 - models.mlp_model - INFO - 使用设备: cuda
2025-06-02 17:47:11,233 - data_loader - INFO - 加载区域信息和能耗标签...
2025-06-02 17:47:11,245 - data_loader - INFO - train集包含 97 个区域
2025-06-02 17:47:11,245 - data_loader - INFO - 加载POI-街景图像映射...
2025-06-02 17:47:11,299 - data_loader - INFO - 成功构建 97 个有效样本
2025-06-02 17:47:11,300 - data_loader - INFO - 加载区域信息和能耗标签...
2025-06-02 17:47:11,311 - data_loader - INFO - val集包含 12 个区域
2025-06-02 17:47:11,312 - data_loader - INFO - 加载POI-街景图像映射...
2025-06-02 17:47:11,320 - data_loader - INFO - 成功构建 12 个有效样本
2025-06-02 17:47:11,321 - data_loader - INFO - 加载区域信息和能耗标签...
2025-06-02 17:47:11,334 - data_loader - INFO - test集包含 13 个区域
2025-06-02 17:47:11,335 - data_loader - INFO - 加载POI-街景图像映射...
2025-06-02 17:47:11,343 - data_loader - INFO - 成功构建 13 个有效样本
2025-06-02 17:47:11,344 - data_loader - INFO - 数据加载器创建完成:
2025-06-02 17:47:11,344 - data_loader - INFO -   训练集: 97 样本
2025-06-02 17:47:11,344 - data_loader - INFO -   验证集: 12 样本
2025-06-02 17:47:11,344 - data_loader - INFO -   测试集: 13 样本
2025-06-02 17:47:11,852 - models.mlp_model - INFO - 开始训练MLP模型，总共 50 个epoch
2025-06-02 17:47:15,790 - models.mlp_model - INFO - 训练批次 0/7, 损失: 1.1009
2025-06-02 17:47:22,673 - models.mlp_model - INFO - Epoch 1/50:
2025-06-02 17:47:22,673 - models.mlp_model - INFO -   训练损失: 1.1741, 训练R²: -3.8422
2025-06-02 17:47:22,673 - models.mlp_model - INFO -   验证损失: 0.8770, 验证R²: -7.9180
2025-06-02 17:47:22,673 - models.mlp_model - INFO -   验证RMSE: 0.9365, 验证MAE: 0.8646
2025-06-02 17:47:22,739 - models.mlp_model - INFO - 保存最佳模型: ./saved_models\shenyang_MLP_best.pt
2025-06-02 17:47:24,105 - models.mlp_model - INFO - 训练批次 0/7, 损失: 1.3387
2025-06-02 17:47:31,373 - models.mlp_model - INFO - Epoch 2/50:
2025-06-02 17:47:31,373 - models.mlp_model - INFO -   训练损失: 1.3027, 训练R²: -3.4787
2025-06-02 17:47:31,374 - models.mlp_model - INFO -   验证损失: 0.7364, 验证R²: -6.4883
2025-06-02 17:47:31,374 - models.mlp_model - INFO -   验证RMSE: 0.8581, 验证MAE: 0.7953
2025-06-02 17:47:31,435 - models.mlp_model - INFO - 保存最佳模型: ./saved_models\shenyang_MLP_best.pt
2025-06-02 17:47:32,802 - models.mlp_model - INFO - 训练批次 0/7, 损失: 1.0481
2025-06-02 17:47:40,074 - models.mlp_model - INFO - Epoch 3/50:
2025-06-02 17:47:40,075 - models.mlp_model - INFO -   训练损失: 0.7929, 训练R²: -2.4481
2025-06-02 17:47:40,075 - models.mlp_model - INFO -   验证损失: 0.4119, 验证R²: -3.1890
2025-06-02 17:47:40,075 - models.mlp_model - INFO -   验证RMSE: 0.6418, 验证MAE: 0.5342
2025-06-02 17:47:40,142 - models.mlp_model - INFO - 保存最佳模型: ./saved_models\shenyang_MLP_best.pt
2025-06-02 17:47:41,516 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.7042
2025-06-02 17:47:49,449 - models.mlp_model - INFO - Epoch 4/50:
2025-06-02 17:47:49,450 - models.mlp_model - INFO -   训练损失: 1.0079, 训练R²: -1.8326
2025-06-02 17:47:49,450 - models.mlp_model - INFO -   验证损失: 0.3166, 验证R²: -2.2200
2025-06-02 17:47:49,450 - models.mlp_model - INFO -   验证RMSE: 0.5627, 验证MAE: 0.4585
2025-06-02 17:47:49,509 - models.mlp_model - INFO - 保存最佳模型: ./saved_models\shenyang_MLP_best.pt
2025-06-02 17:47:50,723 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.6508
2025-06-02 17:47:59,158 - models.mlp_model - INFO - Epoch 5/50:
2025-06-02 17:47:59,158 - models.mlp_model - INFO -   训练损失: 0.7367, 训练R²: -1.7421
2025-06-02 17:47:59,158 - models.mlp_model - INFO -   验证损失: 0.4436, 验证R²: -3.5109
2025-06-02 17:47:59,159 - models.mlp_model - INFO -   验证RMSE: 0.6660, 验证MAE: 0.5656
2025-06-02 17:48:00,917 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.9078
2025-06-02 17:48:10,535 - models.mlp_model - INFO - Epoch 6/50:
2025-06-02 17:48:10,535 - models.mlp_model - INFO -   训练损失: 0.7216, 训练R²: -2.0662
2025-06-02 17:48:10,535 - models.mlp_model - INFO -   验证损失: 0.4320, 验证R²: -3.3932
2025-06-02 17:48:10,536 - models.mlp_model - INFO -   验证RMSE: 0.6573, 验证MAE: 0.5591
2025-06-02 17:48:12,133 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.5025
2025-06-02 17:48:21,910 - models.mlp_model - INFO - Epoch 7/50:
2025-06-02 17:48:21,910 - models.mlp_model - INFO -   训练损失: 1.0564, 训练R²: -1.3597
2025-06-02 17:48:21,910 - models.mlp_model - INFO -   验证损失: 0.7085, 验证R²: -6.2053
2025-06-02 17:48:21,911 - models.mlp_model - INFO -   验证RMSE: 0.8418, 验证MAE: 0.7818
2025-06-02 17:48:23,513 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.8303
2025-06-02 17:48:32,461 - models.mlp_model - INFO - Epoch 8/50:
2025-06-02 17:48:32,462 - models.mlp_model - INFO -   训练损失: 0.5142, 训练R²: -1.1296
2025-06-02 17:48:32,463 - models.mlp_model - INFO -   验证损失: 0.5622, 验证R²: -4.7167
2025-06-02 17:48:32,463 - models.mlp_model - INFO -   验证RMSE: 0.7498, 验证MAE: 0.6825
2025-06-02 17:48:34,069 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.5964
2025-06-02 17:48:43,000 - models.mlp_model - INFO - Epoch 9/50:
2025-06-02 17:48:43,001 - models.mlp_model - INFO -   训练损失: 0.5669, 训练R²: -1.2335
2025-06-02 17:48:43,001 - models.mlp_model - INFO -   验证损失: 1.1102, 验证R²: -10.2901
2025-06-02 17:48:43,001 - models.mlp_model - INFO -   验证RMSE: 1.0537, 验证MAE: 1.0062
2025-06-02 17:48:44,594 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.5235
2025-06-02 17:48:53,373 - models.mlp_model - INFO - Epoch 10/50:
2025-06-02 17:48:53,373 - models.mlp_model - INFO -   训练损失: 0.4785, 训练R²: -0.9381
2025-06-02 17:48:53,374 - models.mlp_model - INFO -   验证损失: 0.5933, 验证R²: -5.0335
2025-06-02 17:48:53,374 - models.mlp_model - INFO -   验证RMSE: 0.7703, 验证MAE: 0.7000
2025-06-02 17:48:54,972 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.5934
2025-06-02 17:49:05,078 - models.mlp_model - INFO - Epoch 11/50:
2025-06-02 17:49:05,079 - models.mlp_model - INFO -   训练损失: 0.5041, 训练R²: -1.1592
2025-06-02 17:49:05,079 - models.mlp_model - INFO -   验证损失: 0.5135, 验证R²: -4.2220
2025-06-02 17:49:05,080 - models.mlp_model - INFO -   验证RMSE: 0.7166, 验证MAE: 0.6441
2025-06-02 17:49:06,687 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.4734
2025-06-02 17:49:16,794 - models.mlp_model - INFO - Epoch 12/50:
2025-06-02 17:49:16,794 - models.mlp_model - INFO -   训练损失: 0.6397, 训练R²: -1.0886
2025-06-02 17:49:16,794 - models.mlp_model - INFO -   验证损失: 0.7254, 验证R²: -6.3768
2025-06-02 17:49:16,795 - models.mlp_model - INFO -   验证RMSE: 0.8517, 验证MAE: 0.7893
2025-06-02 17:49:19,497 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.4711
2025-06-02 17:49:33,357 - models.mlp_model - INFO - Epoch 13/50:
2025-06-02 17:49:33,358 - models.mlp_model - INFO -   训练损失: 0.4630, 训练R²: -0.9947
2025-06-02 17:49:33,358 - models.mlp_model - INFO -   验证损失: 0.5489, 验证R²: -4.5816
2025-06-02 17:49:33,359 - models.mlp_model - INFO -   验证RMSE: 0.7409, 验证MAE: 0.6645
2025-06-02 17:49:35,962 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.2460
2025-06-02 17:49:47,575 - models.mlp_model - INFO - Epoch 14/50:
2025-06-02 17:49:47,576 - models.mlp_model - INFO -   训练损失: 0.3226, 训练R²: -0.3591
2025-06-02 17:49:47,576 - models.mlp_model - INFO -   验证损失: 0.6257, 验证R²: -5.3630
2025-06-02 17:49:47,577 - models.mlp_model - INFO -   验证RMSE: 0.7910, 验证MAE: 0.7135
2025-06-02 17:49:49,676 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.3496
2025-06-02 17:50:00,959 - models.mlp_model - INFO - Epoch 15/50:
2025-06-02 17:50:00,959 - models.mlp_model - INFO -   训练损失: 0.3885, 训练R²: -0.4136
2025-06-02 17:50:00,960 - models.mlp_model - INFO -   验证损失: 0.6132, 验证R²: -5.2354
2025-06-02 17:50:00,960 - models.mlp_model - INFO -   验证RMSE: 0.7831, 验证MAE: 0.7143
2025-06-02 17:50:03,059 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.2819
2025-06-02 17:50:14,837 - models.mlp_model - INFO - Epoch 16/50:
2025-06-02 17:50:14,837 - models.mlp_model - INFO -   训练损失: 0.3498, 训练R²: -0.1258
2025-06-02 17:50:14,838 - models.mlp_model - INFO -   验证损失: 0.5552, 验证R²: -4.6462
2025-06-02 17:50:14,839 - models.mlp_model - INFO -   验证RMSE: 0.7451, 验证MAE: 0.6760
2025-06-02 17:50:16,941 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.3435
2025-06-02 17:50:28,389 - models.mlp_model - INFO - Epoch 17/50:
2025-06-02 17:50:28,390 - models.mlp_model - INFO -   训练损失: 0.7061, 训练R²: -0.7637
2025-06-02 17:50:28,392 - models.mlp_model - INFO -   验证损失: 0.5349, 验证R²: -4.4393
2025-06-02 17:50:28,392 - models.mlp_model - INFO -   验证RMSE: 0.7314, 验证MAE: 0.6487
2025-06-02 17:50:30,492 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.3342
2025-06-02 17:50:42,272 - models.mlp_model - INFO - Epoch 18/50:
2025-06-02 17:50:42,274 - models.mlp_model - INFO -   训练损失: 0.3490, 训练R²: -0.4682
2025-06-02 17:50:42,276 - models.mlp_model - INFO -   验证损失: 0.6403, 验证R²: -5.5114
2025-06-02 17:50:42,277 - models.mlp_model - INFO -   验证RMSE: 0.8002, 验证MAE: 0.7234
2025-06-02 17:50:44,532 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.3263
2025-06-02 17:50:55,993 - models.mlp_model - INFO - Epoch 19/50:
2025-06-02 17:50:55,993 - models.mlp_model - INFO -   训练损失: 0.2780, 训练R²: -0.2103
2025-06-02 17:50:55,993 - models.mlp_model - INFO -   验证损失: 0.6060, 验证R²: -5.1627
2025-06-02 17:50:55,994 - models.mlp_model - INFO -   验证RMSE: 0.7785, 验证MAE: 0.6965
2025-06-02 17:50:58,092 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.3140
2025-06-02 17:51:09,875 - models.mlp_model - INFO - Epoch 20/50:
2025-06-02 17:51:09,876 - models.mlp_model - INFO -   训练损失: 0.4462, 训练R²: -0.8500
2025-06-02 17:51:09,876 - models.mlp_model - INFO -   验证损失: 0.5720, 验证R²: -4.8172
2025-06-02 17:51:09,876 - models.mlp_model - INFO -   验证RMSE: 0.7563, 验证MAE: 0.6731
2025-06-02 17:51:11,971 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.4191
2025-06-02 17:51:23,748 - models.mlp_model - INFO - Epoch 21/50:
2025-06-02 17:51:23,749 - models.mlp_model - INFO -   训练损失: 0.3537, 训练R²: -0.5493
2025-06-02 17:51:23,749 - models.mlp_model - INFO -   验证损失: 0.5405, 验证R²: -4.4960
2025-06-02 17:51:23,750 - models.mlp_model - INFO -   验证RMSE: 0.7352, 验证MAE: 0.6365
2025-06-02 17:51:25,851 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.4174
2025-06-02 17:51:37,299 - models.mlp_model - INFO - Epoch 22/50:
2025-06-02 17:51:37,299 - models.mlp_model - INFO -   训练损失: 0.4721, 训练R²: -0.7708
2025-06-02 17:51:37,300 - models.mlp_model - INFO -   验证损失: 0.5497, 验证R²: -4.5898
2025-06-02 17:51:37,300 - models.mlp_model - INFO -   验证RMSE: 0.7414, 验证MAE: 0.6494
2025-06-02 17:51:39,555 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.3093
2025-06-02 17:51:50,850 - models.mlp_model - INFO - Epoch 23/50:
2025-06-02 17:51:50,850 - models.mlp_model - INFO -   训练损失: 0.3882, 训练R²: -0.4183
2025-06-02 17:51:50,851 - models.mlp_model - INFO -   验证损失: 0.4298, 验证R²: -3.3703
2025-06-02 17:51:50,851 - models.mlp_model - INFO -   验证RMSE: 0.6556, 验证MAE: 0.5610
2025-06-02 17:51:52,950 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.1779
2025-06-02 17:52:04,568 - models.mlp_model - INFO - Epoch 24/50:
2025-06-02 17:52:04,569 - models.mlp_model - INFO -   训练损失: 0.3349, 训练R²: -0.4198
2025-06-02 17:52:04,570 - models.mlp_model - INFO -   验证损失: 0.5678, 验证R²: -4.7737
2025-06-02 17:52:04,570 - models.mlp_model - INFO -   验证RMSE: 0.7535, 验证MAE: 0.6798
2025-06-02 17:52:06,828 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.3689
2025-06-02 17:52:17,121 - models.mlp_model - INFO - Epoch 25/50:
2025-06-02 17:52:17,122 - models.mlp_model - INFO -   训练损失: 0.4110, 训练R²: -0.7605
2025-06-02 17:52:17,123 - models.mlp_model - INFO -   验证损失: 0.5786, 验证R²: -4.8841
2025-06-02 17:52:17,124 - models.mlp_model - INFO -   验证RMSE: 0.7607, 验证MAE: 0.6879
2025-06-02 17:52:18,884 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.2287
2025-06-02 17:52:28,815 - models.mlp_model - INFO - Epoch 26/50:
2025-06-02 17:52:28,816 - models.mlp_model - INFO -   训练损失: 0.5125, 训练R²: -0.4649
2025-06-02 17:52:28,816 - models.mlp_model - INFO -   验证损失: 0.5523, 验证R²: -4.6160
2025-06-02 17:52:28,816 - models.mlp_model - INFO -   验证RMSE: 0.7431, 验证MAE: 0.6682
2025-06-02 17:52:30,590 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.2856
2025-06-02 17:52:40,358 - models.mlp_model - INFO - Epoch 27/50:
2025-06-02 17:52:40,358 - models.mlp_model - INFO -   训练损失: 0.4092, 训练R²: -0.5700
2025-06-02 17:52:40,359 - models.mlp_model - INFO -   验证损失: 0.5489, 验证R²: -4.5815
2025-06-02 17:52:40,359 - models.mlp_model - INFO -   验证RMSE: 0.7409, 验证MAE: 0.6619
2025-06-02 17:52:42,133 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.2549
2025-06-02 17:52:52,084 - models.mlp_model - INFO - Epoch 28/50:
2025-06-02 17:52:52,085 - models.mlp_model - INFO -   训练损失: 0.3178, 训练R²: -0.3631
2025-06-02 17:52:52,085 - models.mlp_model - INFO -   验证损失: 0.4661, 验证R²: -3.7397
2025-06-02 17:52:52,085 - models.mlp_model - INFO -   验证RMSE: 0.6827, 验证MAE: 0.5975
2025-06-02 17:52:53,999 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.4538
2025-06-02 17:53:04,115 - models.mlp_model - INFO - Epoch 29/50:
2025-06-02 17:53:04,115 - models.mlp_model - INFO -   训练损失: 0.3738, 训练R²: -0.6431
2025-06-02 17:53:04,116 - models.mlp_model - INFO -   验证损失: 0.4169, 验证R²: -3.2399
2025-06-02 17:53:04,116 - models.mlp_model - INFO -   验证RMSE: 0.6457, 验证MAE: 0.5568
2025-06-02 17:53:06,052 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.4974
2025-06-02 17:53:15,999 - models.mlp_model - INFO - Epoch 30/50:
2025-06-02 17:53:15,999 - models.mlp_model - INFO -   训练损失: 0.3292, 训练R²: -0.4192
2025-06-02 17:53:15,999 - models.mlp_model - INFO -   验证损失: 0.4482, 验证R²: -3.5579
2025-06-02 17:53:15,999 - models.mlp_model - INFO -   验证RMSE: 0.6695, 验证MAE: 0.5828
2025-06-02 17:53:17,775 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.2320
2025-06-02 17:53:27,391 - models.mlp_model - INFO - Epoch 31/50:
2025-06-02 17:53:27,392 - models.mlp_model - INFO -   训练损失: 0.4718, 训练R²: -0.3799
2025-06-02 17:53:27,392 - models.mlp_model - INFO -   验证损失: 0.4215, 验证R²: -3.2858
2025-06-02 17:53:27,393 - models.mlp_model - INFO -   验证RMSE: 0.6492, 验证MAE: 0.5531
2025-06-02 17:53:29,309 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.3161
2025-06-02 17:53:39,423 - models.mlp_model - INFO - Epoch 32/50:
2025-06-02 17:53:39,423 - models.mlp_model - INFO -   训练损失: 0.3248, 训练R²: -0.4364
2025-06-02 17:53:39,424 - models.mlp_model - INFO -   验证损失: 0.4141, 验证R²: -3.2113
2025-06-02 17:53:39,424 - models.mlp_model - INFO -   验证RMSE: 0.6435, 验证MAE: 0.5373
2025-06-02 17:53:41,200 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.2648
2025-06-02 17:53:51,301 - models.mlp_model - INFO - Epoch 33/50:
2025-06-02 17:53:51,301 - models.mlp_model - INFO -   训练损失: 0.3344, 训练R²: -0.4776
2025-06-02 17:53:51,301 - models.mlp_model - INFO -   验证损失: 0.5006, 验证R²: -4.0908
2025-06-02 17:53:51,302 - models.mlp_model - INFO -   验证RMSE: 0.7075, 验证MAE: 0.6164
2025-06-02 17:53:53,235 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.3968
2025-06-02 17:54:03,846 - models.mlp_model - INFO - Epoch 34/50:
2025-06-02 17:54:03,847 - models.mlp_model - INFO -   训练损失: 0.3608, 训练R²: -0.5673
2025-06-02 17:54:03,847 - models.mlp_model - INFO -   验证损失: 0.5644, 验证R²: -4.7394
2025-06-02 17:54:03,848 - models.mlp_model - INFO -   验证RMSE: 0.7513, 验证MAE: 0.6615
2025-06-02 17:54:05,779 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.2039
2025-06-02 17:54:15,890 - models.mlp_model - INFO - Epoch 35/50:
2025-06-02 17:54:15,890 - models.mlp_model - INFO -   训练损失: 0.3878, 训练R²: -0.4117
2025-06-02 17:54:15,891 - models.mlp_model - INFO -   验证损失: 0.5666, 验证R²: -4.7618
2025-06-02 17:54:15,891 - models.mlp_model - INFO -   验证RMSE: 0.7527, 验证MAE: 0.6653
2025-06-02 17:54:17,830 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.2936
2025-06-02 17:54:27,943 - models.mlp_model - INFO - Epoch 36/50:
2025-06-02 17:54:27,943 - models.mlp_model - INFO -   训练损失: 0.3019, 训练R²: -0.3191
2025-06-02 17:54:27,943 - models.mlp_model - INFO -   验证损失: 0.4810, 验证R²: -3.8917
2025-06-02 17:54:27,944 - models.mlp_model - INFO -   验证RMSE: 0.6936, 验证MAE: 0.6132
2025-06-02 17:54:29,877 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.5319
2025-06-02 17:54:40,173 - models.mlp_model - INFO - Epoch 37/50:
2025-06-02 17:54:40,173 - models.mlp_model - INFO -   训练损失: 0.3504, 训练R²: -0.4859
2025-06-02 17:54:40,174 - models.mlp_model - INFO -   验证损失: 0.4927, 验证R²: -4.0100
2025-06-02 17:54:40,174 - models.mlp_model - INFO -   验证RMSE: 0.7019, 验证MAE: 0.6267
2025-06-02 17:54:42,425 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.3197
2025-06-02 17:54:54,223 - models.mlp_model - INFO - Epoch 38/50:
2025-06-02 17:54:54,223 - models.mlp_model - INFO -   训练损失: 0.4447, 训练R²: -0.4135
2025-06-02 17:54:54,224 - models.mlp_model - INFO -   验证损失: 0.4752, 验证R²: -3.8322
2025-06-02 17:54:54,224 - models.mlp_model - INFO -   验证RMSE: 0.6893, 验证MAE: 0.6166
2025-06-02 17:54:56,472 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.1249
2025-06-02 17:55:08,092 - models.mlp_model - INFO - Epoch 39/50:
2025-06-02 17:55:08,092 - models.mlp_model - INFO -   训练损失: 0.2994, 训练R²: -0.2307
2025-06-02 17:55:08,092 - models.mlp_model - INFO -   验证损失: 0.4986, 验证R²: -4.0707
2025-06-02 17:55:08,093 - models.mlp_model - INFO -   验证RMSE: 0.7061, 验证MAE: 0.6343
2025-06-02 17:55:10,195 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.4125
2025-06-02 17:55:21,478 - models.mlp_model - INFO - Epoch 40/50:
2025-06-02 17:55:21,478 - models.mlp_model - INFO -   训练损失: 0.3468, 训练R²: -0.4674
2025-06-02 17:55:21,478 - models.mlp_model - INFO -   验证损失: 0.5310, 验证R²: -4.4000
2025-06-02 17:55:21,479 - models.mlp_model - INFO -   验证RMSE: 0.7287, 验证MAE: 0.6584
2025-06-02 17:55:23,578 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.2936
2025-06-02 17:55:35,195 - models.mlp_model - INFO - Epoch 41/50:
2025-06-02 17:55:35,195 - models.mlp_model - INFO -   训练损失: 0.3169, 训练R²: -0.3811
2025-06-02 17:55:35,196 - models.mlp_model - INFO -   验证损失: 0.5295, 验证R²: -4.3845
2025-06-02 17:55:35,196 - models.mlp_model - INFO -   验证RMSE: 0.7277, 验证MAE: 0.6569
2025-06-02 17:55:37,298 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.3695
2025-06-02 17:55:48,745 - models.mlp_model - INFO - Epoch 42/50:
2025-06-02 17:55:48,746 - models.mlp_model - INFO -   训练损失: 0.3583, 训练R²: -0.2331
2025-06-02 17:55:48,747 - models.mlp_model - INFO -   验证损失: 0.5095, 验证R²: -4.1808
2025-06-02 17:55:48,748 - models.mlp_model - INFO -   验证RMSE: 0.7138, 验证MAE: 0.6451
2025-06-02 17:55:50,852 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.2251
2025-06-02 17:56:02,461 - models.mlp_model - INFO - Epoch 43/50:
2025-06-02 17:56:02,461 - models.mlp_model - INFO -   训练损失: 0.4526, 训练R²: -0.2004
2025-06-02 17:56:02,462 - models.mlp_model - INFO -   验证损失: 0.5617, 验证R²: -4.7117
2025-06-02 17:56:02,462 - models.mlp_model - INFO -   验证RMSE: 0.7494, 验证MAE: 0.6820
2025-06-02 17:56:04,557 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.2438
2025-06-02 17:56:16,006 - models.mlp_model - INFO - Epoch 44/50:
2025-06-02 17:56:16,006 - models.mlp_model - INFO -   训练损失: 0.3334, 训练R²: -0.1370
2025-06-02 17:56:16,007 - models.mlp_model - INFO -   验证损失: 0.6551, 验证R²: -5.6619
2025-06-02 17:56:16,007 - models.mlp_model - INFO -   验证RMSE: 0.8094, 验证MAE: 0.7431
2025-06-02 17:56:18,107 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.2974
2025-06-02 17:56:29,889 - models.mlp_model - INFO - Epoch 45/50:
2025-06-02 17:56:29,889 - models.mlp_model - INFO -   训练损失: 0.4020, 训练R²: -0.4765
2025-06-02 17:56:29,890 - models.mlp_model - INFO -   验证损失: 0.6250, 验证R²: -5.3557
2025-06-02 17:56:29,890 - models.mlp_model - INFO -   验证RMSE: 0.7906, 验证MAE: 0.7204
2025-06-02 17:56:32,142 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.3522
2025-06-02 17:56:45,595 - models.mlp_model - INFO - Epoch 46/50:
2025-06-02 17:56:45,595 - models.mlp_model - INFO -   训练损失: 0.3222, 训练R²: -0.3390
2025-06-02 17:56:45,596 - models.mlp_model - INFO -   验证损失: 0.5858, 验证R²: -4.9569
2025-06-02 17:56:45,597 - models.mlp_model - INFO -   验证RMSE: 0.7654, 验证MAE: 0.6942
2025-06-02 17:56:47,868 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.5200
2025-06-02 17:56:59,485 - models.mlp_model - INFO - Epoch 47/50:
2025-06-02 17:56:59,485 - models.mlp_model - INFO -   训练损失: 0.2683, 训练R²: -0.1861
2025-06-02 17:56:59,486 - models.mlp_model - INFO -   验证损失: 0.5532, 验证R²: -4.6259
2025-06-02 17:56:59,486 - models.mlp_model - INFO -   验证RMSE: 0.7438, 验证MAE: 0.6713
2025-06-02 17:57:01,581 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.3205
2025-06-02 17:57:13,032 - models.mlp_model - INFO - Epoch 48/50:
2025-06-02 17:57:13,033 - models.mlp_model - INFO -   训练损失: 0.3891, 训练R²: -0.3021
2025-06-02 17:57:13,033 - models.mlp_model - INFO -   验证损失: 0.5180, 验证R²: -4.2673
2025-06-02 17:57:13,033 - models.mlp_model - INFO -   验证RMSE: 0.7197, 验证MAE: 0.6463
2025-06-02 17:57:15,296 - models.mlp_model - INFO - 训练批次 0/7, 损失: 0.2631
