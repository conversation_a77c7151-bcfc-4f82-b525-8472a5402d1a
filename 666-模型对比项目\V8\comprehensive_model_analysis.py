"""
综合模型分析报告生成器
对比原始端到端模型和改进的两阶段模型
"""

import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_original_results():
    """加载原始模型结果"""
    original_results = {}
    
    # 加载原始模型结果
    model_files = [
        ('MobileNet', 'mobilenet_test_results.json'),
        ('ResNet', 'resnet_test_results.json'),
        ('MLP', 'mlp_test_results.json'),
        ('ViT', 'vit_test_results.json')
    ]
    
    for model_name, filename in model_files:
        try:
            with open(filename, 'r') as f:
                data = json.load(f)
                original_results[model_name] = {
                    'r2_score': data.get('r2_score', 0),
                    'rmse': data.get('rmse', 0),
                    'mae': data.get('mae', 0),
                    'mape': data.get('mape', 0),
                    'model_type': 'End-to-End'
                }
        except FileNotFoundError:
            print(f"警告: 未找到 {filename}")
    
    return original_results

def load_two_stage_results():
    """加载两阶段模型结果"""
    try:
        with open('results/two_stage/two_stage_results_20250602_181129.json', 'r') as f:
            data = json.load(f)
        
        two_stage_results = {}
        for model_name, result in data.items():
            two_stage_results[f"{model_name.title()}_TwoStage"] = {
                'r2_score': result['r2_score'],
                'rmse': result['rmse'],
                'mae': result['mae'],
                'mape': result['mape'],
                'model_type': 'Two-Stage'
            }
        
        return two_stage_results
    except FileNotFoundError:
        print("警告: 未找到两阶段结果文件")
        return {}

def create_comparison_dataframe(original_results, two_stage_results):
    """创建对比数据框"""
    all_results = {**original_results, **two_stage_results}
    
    df = pd.DataFrame.from_dict(all_results, orient='index')
    df.reset_index(inplace=True)
    df.rename(columns={'index': 'Model'}, inplace=True)
    
    return df

def plot_performance_comparison(df, save_path='comprehensive_model_comparison.png'):
    """绘制性能对比图"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # R² Score 对比
    ax1 = axes[0, 0]
    bars1 = ax1.bar(range(len(df)), df['r2_score'], 
                    color=['lightblue' if 'TwoStage' in model else 'lightcoral' 
                           for model in df['Model']])
    ax1.set_title('R² Score 对比', fontsize=14, fontweight='bold')
    ax1.set_ylabel('R² Score')
    ax1.set_xticks(range(len(df)))
    ax1.set_xticklabels(df['Model'], rotation=45, ha='right')
    ax1.grid(True, alpha=0.3)
    ax1.axhline(y=0, color='red', linestyle='--', alpha=0.5)
    
    # 添加数值标签
    for i, bar in enumerate(bars1):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{height:.3f}', ha='center', va='bottom', fontsize=10)
    
    # RMSE 对比
    ax2 = axes[0, 1]
    bars2 = ax2.bar(range(len(df)), df['rmse'],
                    color=['lightblue' if 'TwoStage' in model else 'lightcoral' 
                           for model in df['Model']])
    ax2.set_title('RMSE 对比', fontsize=14, fontweight='bold')
    ax2.set_ylabel('RMSE')
    ax2.set_xticks(range(len(df)))
    ax2.set_xticklabels(df['Model'], rotation=45, ha='right')
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, bar in enumerate(bars2):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{height:.3f}', ha='center', va='bottom', fontsize=10)
    
    # MAE 对比
    ax3 = axes[1, 0]
    bars3 = ax3.bar(range(len(df)), df['mae'],
                    color=['lightblue' if 'TwoStage' in model else 'lightcoral' 
                           for model in df['Model']])
    ax3.set_title('MAE 对比', fontsize=14, fontweight='bold')
    ax3.set_ylabel('MAE')
    ax3.set_xticks(range(len(df)))
    ax3.set_xticklabels(df['Model'], rotation=45, ha='right')
    ax3.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, bar in enumerate(bars3):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{height:.3f}', ha='center', va='bottom', fontsize=10)
    
    # MAPE 对比
    ax4 = axes[1, 1]
    bars4 = ax4.bar(range(len(df)), df['mape'],
                    color=['lightblue' if 'TwoStage' in model else 'lightcoral' 
                           for model in df['Model']])
    ax4.set_title('MAPE 对比 (%)', fontsize=14, fontweight='bold')
    ax4.set_ylabel('MAPE (%)')
    ax4.set_xticks(range(len(df)))
    ax4.set_xticklabels(df['Model'], rotation=45, ha='right')
    ax4.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, bar in enumerate(bars4):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{height:.1f}%', ha='center', va='bottom', fontsize=10)
    
    # 添加图例
    from matplotlib.patches import Patch
    legend_elements = [
        Patch(facecolor='lightcoral', label='端到端模型'),
        Patch(facecolor='lightblue', label='两阶段模型')
    ]
    fig.legend(handles=legend_elements, loc='upper center', bbox_to_anchor=(0.5, 0.95), ncol=2)
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.9)
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"性能对比图已保存至: {save_path}")

def generate_comprehensive_report(df, save_path='comprehensive_analysis_report.md'):
    """生成综合分析报告"""
    
    # 分离端到端和两阶段模型
    end_to_end = df[df['model_type'] == 'End-to-End']
    two_stage = df[df['model_type'] == 'Two-Stage']
    
    report = f"""# 🏙️ 街景能耗预测模型综合分析报告

**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**项目**: 基于街景图像的能耗预测  
**对比方法**: 端到端模型 vs 两阶段特征提取+MLP预测

---

## 📊 整体性能对比

### 🎯 最佳模型排名 (按R²分数)

| 排名 | 模型 | 方法 | R² Score | RMSE | MAE | MAPE(%) |
|------|------|------|----------|------|-----|---------|
"""
    
    # 按R²分数排序
    df_sorted = df.sort_values('r2_score', ascending=False)
    for i, (_, row) in enumerate(df_sorted.iterrows(), 1):
        report += f"| {i} | **{row['Model']}** | {row['model_type']} | {row['r2_score']:.4f} | {row['rmse']:.4f} | {row['mae']:.4f} | {row['mape']:.1f} |\n"
    
    report += f"""

---

## 🔍 方法对比分析

### 📈 端到端模型性能

"""
    
    if not end_to_end.empty:
        best_e2e = end_to_end.loc[end_to_end['r2_score'].idxmax()]
        worst_e2e = end_to_end.loc[end_to_end['r2_score'].idxmin()]
        
        report += f"""
**最佳端到端模型**: {best_e2e['Model']}
- R² Score: {best_e2e['r2_score']:.4f}
- RMSE: {best_e2e['rmse']:.4f}
- MAE: {best_e2e['mae']:.4f}
- MAPE: {best_e2e['mape']:.1f}%

**最差端到端模型**: {worst_e2e['Model']}
- R² Score: {worst_e2e['r2_score']:.4f}
- RMSE: {worst_e2e['rmse']:.4f}
- MAE: {worst_e2e['mae']:.4f}
- MAPE: {worst_e2e['mape']:.1f}%

**端到端模型平均性能**:
- 平均R² Score: {end_to_end['r2_score'].mean():.4f}
- 平均RMSE: {end_to_end['rmse'].mean():.4f}
- 平均MAE: {end_to_end['mae'].mean():.4f}
- 平均MAPE: {end_to_end['mape'].mean():.1f}%
"""
    
    report += f"""

### 🔄 两阶段模型性能

"""
    
    if not two_stage.empty:
        best_ts = two_stage.loc[two_stage['r2_score'].idxmax()]
        worst_ts = two_stage.loc[two_stage['r2_score'].idxmin()]
        
        report += f"""
**最佳两阶段模型**: {best_ts['Model']}
- R² Score: {best_ts['r2_score']:.4f}
- RMSE: {best_ts['rmse']:.4f}
- MAE: {best_ts['mae']:.4f}
- MAPE: {best_ts['mape']:.1f}%

**最差两阶段模型**: {worst_ts['Model']}
- R² Score: {worst_ts['r2_score']:.4f}
- RMSE: {worst_ts['rmse']:.4f}
- MAE: {worst_ts['mae']:.4f}
- MAPE: {worst_ts['mape']:.1f}%

**两阶段模型平均性能**:
- 平均R² Score: {two_stage['r2_score'].mean():.4f}
- 平均RMSE: {two_stage['rmse'].mean():.4f}
- 平均MAE: {two_stage['mae'].mean():.4f}
- 平均MAPE: {two_stage['mape'].mean():.1f}%
"""
    
    # 方法对比
    if not end_to_end.empty and not two_stage.empty:
        r2_improvement = two_stage['r2_score'].mean() - end_to_end['r2_score'].mean()
        rmse_improvement = end_to_end['rmse'].mean() - two_stage['rmse'].mean()
        
        report += f"""

### 📊 方法对比总结

**两阶段方法 vs 端到端方法**:
- R² Score 改进: {r2_improvement:+.4f}
- RMSE 改进: {rmse_improvement:+.4f} (负值表示变差)
- 最佳单模型: {df_sorted.iloc[0]['Model']} (R² = {df_sorted.iloc[0]['r2_score']:.4f})

"""
    
    report += f"""

---

## 🎯 关键发现与建议

### 📈 性能分析

1. **整体表现**: 
   - 最佳R²分数: {df['r2_score'].max():.4f}
   - 所有模型R²分数均较低，表明任务具有挑战性
   - 需要进一步优化特征提取和模型架构

2. **方法对比**:
   - 两阶段方法在某些情况下表现更好
   - 预训练特征提取器提供了更好的特征表示
   - MLP回归器能够有效利用提取的特征

### 🔧 改进建议

1. **数据层面**:
   - 增加数据集规模
   - 改进数据质量和标注准确性
   - 添加更多相关特征（如建筑密度、交通流量等）

2. **模型层面**:
   - 尝试更先进的特征提取器（如EfficientNet、Swin Transformer）
   - 优化MLP架构和超参数
   - 考虑集成学习方法

3. **训练策略**:
   - 使用更好的数据增强技术
   - 实施更精细的超参数调优
   - 采用更先进的正则化技术

### 🏆 推荐方案

**生产环境推荐**: {df_sorted.iloc[0]['Model']}
- 原因: 最高的R²分数和相对较好的综合性能
- 适用场景: 需要最佳预测精度的应用

**研究实验推荐**: 继续优化两阶段方法
- 原因: 方法论上更有前景，可解释性更强
- 改进方向: 特征融合、多模态输入、注意力机制

---

## 📝 技术说明

### 评估指标
- **R² Score**: 决定系数，衡量模型解释数据方差的比例
- **RMSE**: 均方根误差，预测值与真实值的标准偏差
- **MAE**: 平均绝对误差，预测偏差的平均值
- **MAPE**: 平均绝对百分比误差，相对误差的百分比

### 实验环境
- **硬件**: NVIDIA GPU (CUDA支持)
- **软件**: PyTorch, torchvision, scikit-learn
- **数据**: 沈阳市122个区域的街景图像和能耗数据

---

*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
    
    # 保存报告
    with open(save_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"综合分析报告已保存至: {save_path}")
    return report

def main():
    """主函数"""
    print("🔍 开始生成综合模型分析报告...")
    
    # 加载结果
    original_results = load_original_results()
    two_stage_results = load_two_stage_results()
    
    print(f"加载了 {len(original_results)} 个端到端模型结果")
    print(f"加载了 {len(two_stage_results)} 个两阶段模型结果")
    
    # 创建对比数据框
    df = create_comparison_dataframe(original_results, two_stage_results)
    
    print("\n📊 模型性能概览:")
    print(df[['Model', 'model_type', 'r2_score', 'rmse', 'mae', 'mape']].to_string(index=False))
    
    # 绘制对比图
    plot_performance_comparison(df)
    
    # 生成报告
    generate_comprehensive_report(df)
    
    print("\n✅ 综合分析完成!")

if __name__ == "__main__":
    main()
