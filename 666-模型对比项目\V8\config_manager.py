"""
项目配置管理
集中管理所有模型训练、数据处理和系统配置
"""

import os
import json
import torch
from pathlib import Path

class ProjectConfig:
    """项目配置类"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.setup_default_config()
    
    def setup_default_config(self):
        """设置默认配置"""
        
        # 数据集配置
        self.data_config = {
            'shenyang': {
                'region2allinfo_path': r"D:\研二\能耗估算\666-模型对比项目\V6\data\shenyang\shenyang_region2allinfo.json",
                'train_idx_path': './data/shenyang/shenyang_zl15_train.csv',
                'val_idx_path': './data/shenyang/shenyang_zl15_valid.csv',
                'test_idx_path': './data/shenyang/shenyang_zl15_test.csv',
                'streetview_image_dir': './data/shenyang/streetview_image/Region/',
                'poi_streetview_filename_path': './data/shenyang/streetview_image/region_5_10_poi_image_filename.json',
                'satellite_image_dir': './data/shenyang/satellite_image/zl15_224/',  # 可选
                'kg_path': './data/shenyang/kg_without_building.txt',  # 可选
                'pretrained_embeddings': 'D:/研二/能耗估算/666-模型对比项目/V4/data/shenyang/预训练数据/ER_shenhe_TuckER_64.npz'  # 可选
            }
        }
        
        # 训练配置
        self.training_config = {
            'default': {
                'epochs': 50,
                'batch_size': 'auto',  # 根据GPU自动选择
                'learning_rate': 'auto',  # 根据模型自动选择
                'weight_decay': 1e-4,
                'max_images_per_region': 3,
                'num_workers': 0,  # Windows建议使用0
                'pin_memory': True,
                'early_stopping_patience': 15,
                'gradient_clip_norm': 1.0
            },
            
            # GPU特定配置
            'gpu_configs': {
                'gtx_1050': {
                    'batch_size': 4,
                    'max_images_per_region': 2,
                    'mixed_precision': False,
                    'memory_efficient': True
                },
                'rtx_3080': {
                    'batch_size': 16,
                    'max_images_per_region': 5,
                    'mixed_precision': True,
                    'memory_efficient': False
                },
                'rtx_4090': {
                    'batch_size': 32,
                    'max_images_per_region': 8,
                    'mixed_precision': True,
                    'memory_efficient': False
                },
                'cpu': {
                    'batch_size': 2,
                    'max_images_per_region': 1,
                    'mixed_precision': False,
                    'memory_efficient': True
                }
            }
        }
        
        # 模型配置
        self.model_configs = {
            'MLP': {
                'input_dim': 128,
                'hidden_dims': [256, 128, 64],
                'output_dim': 1,
                'dropout': 0.3,
                'learning_rate': 0.001,
                'optimizer': 'Adam',
                'scheduler': 'ReduceLROnPlateau'
            },
            
            'MobileNet': {
                'output_dim': 128,
                'dropout': 0.2,
                'pretrained': True,
                'width_multiplier': 1.0,
                'learning_rate': 0.001,
                'optimizer': 'SGD',
                'momentum': 0.9,
                'scheduler': 'StepLR',
                'step_size': 15,
                'gamma': 0.7
            },
            
            'EfficientNet': {
                'model_name': 'efficientnet_b0',
                'output_dim': 128,
                'dropout': 0.2,
                'pretrained': True,
                'learning_rate': 0.001,
                'optimizer': 'Adam',
                'scheduler': 'ReduceLROnPlateau'
            },
            
            'ResNet': {
                'model_name': 'resnet50',
                'output_dim': 128,
                'dropout': 0.5,
                'pretrained': True,
                'learning_rate': 0.001,
                'optimizer': 'Adam',
                'scheduler': 'ReduceLROnPlateau'
            },
            
            'ViT': {
                'img_size': 224,
                'patch_size': 16,
                'embed_dim': 192,  # 轻量级版本
                'n_layers': 6,
                'n_heads': 4,
                'mlp_ratio': 4,
                'output_dim': 128,
                'dropout': 0.1,
                'learning_rate': 0.0003,
                'optimizer': 'AdamW',
                'scheduler': 'CosineAnnealingLR',
                'weight_decay': 0.1
            }
        }
        
        # 系统配置
        self.system_config = {
            'save_dir': './saved_models',
            'results_dir': './results',
            'logs_dir': './logs',
            'cache_dir': './cache',
            'temp_dir': './temp',
            
            # 日志配置
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'file_handler': True,
                'console_handler': True
            },
            
            # 可视化配置
            'visualization': {
                'dpi': 300,
                'figure_size': (12, 8),
                'font_size': 12,
                'style': 'seaborn',
                'save_format': 'png'
            },
            
            # 性能监控
            'monitoring': {
                'gpu_memory_check': True,
                'training_metrics': ['loss', 'r2', 'rmse', 'mae'],
                'save_checkpoints': True,
                'checkpoint_frequency': 10
            }
        }
        
        # 硬件检测和配置
        self.hardware_config = self.detect_hardware()
    
    def detect_hardware(self):
        """检测硬件配置"""
        config = {
            'device': 'cpu',
            'gpu_name': 'No GPU',
            'gpu_memory': 0,
            'recommended_batch_size': 2,
            'recommended_models': ['MLP', 'MobileNet']
        }
        
        if torch.cuda.is_available():
            config['device'] = 'cuda'
            config['gpu_name'] = torch.cuda.get_device_name(0)
            config['gpu_memory'] = torch.cuda.get_device_properties(0).total_memory / 1024**3
            
            # 根据GPU型号推荐配置
            gpu_name = config['gpu_name'].lower()
            if 'gtx 1050' in gpu_name or config['gpu_memory'] < 4:
                config['gpu_type'] = 'gtx_1050'
                config['recommended_batch_size'] = 4
                config['recommended_models'] = ['MobileNet', 'EfficientNet']
            elif 'rtx 3080' in gpu_name or 'rtx 3070' in gpu_name:
                config['gpu_type'] = 'rtx_3080'
                config['recommended_batch_size'] = 16
                config['recommended_models'] = ['MobileNet', 'EfficientNet', 'ResNet', 'ViT']
            elif 'rtx 4090' in gpu_name or 'rtx 4080' in gpu_name:
                config['gpu_type'] = 'rtx_4090'
                config['recommended_batch_size'] = 32
                config['recommended_models'] = ['MobileNet', 'EfficientNet', 'ResNet', 'ViT']
            else:
                config['gpu_type'] = 'other'
                config['recommended_batch_size'] = 8
                config['recommended_models'] = ['MobileNet', 'EfficientNet', 'ResNet']
        
        return config
    
    def get_training_config(self, model_name, custom_config=None):
        """获取特定模型的训练配置"""
        # 基础配置
        base_config = self.training_config['default'].copy()
        
        # GPU特定配置
        gpu_type = self.hardware_config.get('gpu_type', 'cpu')
        if gpu_type in self.training_config['gpu_configs']:
            gpu_config = self.training_config['gpu_configs'][gpu_type]
            base_config.update(gpu_config)
        
        # 模型特定配置
        if model_name in self.model_configs:
            model_config = self.model_configs[model_name].copy()
            
            # 处理自动参数
            if base_config['batch_size'] == 'auto':
                base_config['batch_size'] = self.hardware_config['recommended_batch_size']
            
            if model_config.get('learning_rate') == 'auto':
                model_config['learning_rate'] = self.get_auto_learning_rate(model_name)
            
            # 合并配置
            final_config = {**base_config, **model_config}
        else:
            final_config = base_config
        
        # 应用自定义配置
        if custom_config:
            final_config.update(custom_config)
        
        return final_config
    
    def get_auto_learning_rate(self, model_name):
        """自动选择学习率"""
        lr_map = {
            'MLP': 0.001,
            'MobileNet': 0.001,
            'EfficientNet': 0.001,
            'ResNet': 0.001,
            'ViT': 0.0003
        }
        return lr_map.get(model_name, 0.001)
    
    def create_directories(self):
        """创建必要的目录"""
        dirs = [
            self.system_config['save_dir'],
            self.system_config['results_dir'],
            self.system_config['logs_dir'],
            self.system_config['cache_dir'],
            self.system_config['temp_dir']
        ]
        
        for dir_path in dirs:
            os.makedirs(dir_path, exist_ok=True)
    
    def save_config(self, filepath='project_config.json'):
        """保存配置到文件"""
        config_dict = {
            'data_config': self.data_config,
            'training_config': self.training_config,
            'model_configs': self.model_configs,
            'system_config': self.system_config,
            'hardware_config': self.hardware_config
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False, default=str)
    
    def load_config(self, filepath='project_config.json'):
        """从文件加载配置"""
        if os.path.exists(filepath):
            with open(filepath, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
            
            self.data_config = config_dict.get('data_config', self.data_config)
            self.training_config = config_dict.get('training_config', self.training_config)
            self.model_configs = config_dict.get('model_configs', self.model_configs)
            self.system_config = config_dict.get('system_config', self.system_config)
            # hardware_config 不从文件加载，总是检测当前硬件
    
    def get_data_config(self, dataset='shenyang'):
        """获取数据配置"""
        return self.data_config.get(dataset, self.data_config['shenyang'])
    
    def print_hardware_info(self):
        """打印硬件信息"""
        hw = self.hardware_config
        print("🖥️  硬件配置信息:")
        print(f"   设备: {hw['device']}")
        print(f"   GPU: {hw['gpu_name']}")
        if hw['gpu_memory'] > 0:
            print(f"   显存: {hw['gpu_memory']:.1f} GB")
        print(f"   推荐批次大小: {hw['recommended_batch_size']}")
        print(f"   推荐模型: {', '.join(hw['recommended_models'])}")
    
    def print_training_recommendations(self):
        """打印训练推荐"""
        hw = self.hardware_config
        print("\n💡 训练建议:")
        
        if hw['device'] == 'cpu':
            print("   - 检测到CPU模式，建议使用MobileNet模型")
            print("   - 使用小批次大小 (batch_size=2)")
            print("   - 训练时间会较长，请耐心等待")
        elif 'gtx 1050' in hw['gpu_name'].lower():
            print("   - 检测到GTX 1050，推荐使用轻量级模型")
            print("   - 首选: MobileNet (最快, 显存需求最低)")
            print("   - 备选: EfficientNet (平衡性能和效率)")
            print("   - 避免: ViT (显存需求较高)")
        elif hw['gpu_memory'] >= 8:
            print("   - 检测到高端GPU，可以使用所有模型")
            print("   - 推荐尝试不同模型进行对比")
            print("   - 可以使用较大的批次大小提升训练速度")
        else:
            print("   - 中等GPU配置，推荐使用中等复杂度模型")
            print("   - 推荐: MobileNet, EfficientNet, ResNet")

# 全局配置实例
config = ProjectConfig()

def get_config():
    """获取全局配置实例"""
    return config

def setup_environment():
    """设置项目环境"""
    config.create_directories()
    config.save_config()
    
    print("🚀 项目环境设置完成!")
    config.print_hardware_info()
    config.print_training_recommendations()

if __name__ == "__main__":
    # 演示配置使用
    setup_environment()
    
    # 示例: 获取MobileNet的训练配置
    mobilenet_config = config.get_training_config('MobileNet')
    print(f"\nMobileNet训练配置: {mobilenet_config}")
    
    # 示例: 获取数据配置
    data_config = config.get_data_config()
    print(f"\n数据配置: {list(data_config.keys())}")
