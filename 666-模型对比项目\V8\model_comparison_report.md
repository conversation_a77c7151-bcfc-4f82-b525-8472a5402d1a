# 🏙️ 街景能耗预测模型对比分析报告

**生成时间**: 2025-06-02 17:59:38  
**测试数据集**: 沈阳市街景能耗数据集  
**模型数量**: 4 个  
**评估指标**: R² Score, RMSE, MAE, MAPE

---

## 📊 模型性能排行榜

### 🏆 TOP 10 模型排名

| 排名 | 模型 | R² Score | RMSE | MAE | MAPE(%) | 参数量 | 类型 | 推荐等级 |
|------|------|----------|------|-----|---------|--------|------|----------|
| 1 | **MobileNet** | 0.159 | 0.559 | 0.448 | 49.2 | 3.5M | 轻量级CNN | ⭐ |
| 2 | **ResNet** | -0.277 | 0.689 | 0.510 | 52.9 | 25.6M | 深度残差网络 | ⭐ |
| 3 | **MLP** | -0.604 | 0.772 | 0.641 | 71.2 | 1.0M | 基线模型 | ⭐ |
| 4 | **ViT** | -0.607 | 0.669 | 0.481 | 41.9 | 8.0M | Transformer | ⭐ |


---

## 📈 详细性能分析

### 🥇 最佳性能模型

**MobileNet** (MobileNet-V2)
- **R² Score**: 0.159 (解释了 15.9% 的方差)
- **RMSE**: 0.559
- **MAE**: 0.448
- **MAPE**: 49.2%
- **模型类型**: 轻量级CNN
- **参数量**: 3.5M

### 🚀 效率之选


**MobileNet** - 高效能模型
- R² Score: 0.159 | 参数量: 3.5M | 效率评分: 100.0/100


---

## 🎯 模型选择建议

### 按使用场景推荐

#### 🏭 生产环境部署
1. **首选**: MobileNet - 最佳精度表现
2. **备选**: ResNet - 性能与效率平衡
3. **轻量**: MobileNet - 资源友好

#### 💻 边缘计算设备
1. **MobileNet** (参数量: 3.5M, R²: 0.159)
2. **ResNet** (参数量: 25.6M, R²: -0.277)
3. **MLP** (参数量: 1.0M, R²: -0.604)


#### 🔬 研究实验
1. **Transformer**: ViT
2. **现代CNN**: ConvNeXt

---

## 📋 模型技术特点


### MobileNet (MobileNet-V2)

**技术特点**:
- 🏗️ **架构类型**: 轻量级CNN
- 📊 **参数规模**: 3.5M
- 🎯 **R² Score**: 0.159
- ⚡ **综合评分**: 100.0/100

**适用场景**: 基线对比

### ResNet (ResNet-50)

**技术特点**:
- 🏗️ **架构类型**: 深度残差网络
- 📊 **参数规模**: 25.6M
- 🎯 **R² Score**: -0.277
- ⚡ **综合评分**: -18.8/100

**适用场景**: 基线对比

### MLP (多层感知器)

**技术特点**:
- 🏗️ **架构类型**: 基线模型
- 📊 **参数规模**: 1.0M
- 🎯 **R² Score**: -0.604
- ⚡ **综合评分**: -109.2/100

**适用场景**: 基线对比

### ViT (Vision Transformer)

**技术特点**:
- 🏗️ **架构类型**: Transformer
- 📊 **参数规模**: 8.0M
- 🎯 **R² Score**: -0.607
- ⚡ **综合评分**: -99.8/100

**适用场景**: 基线对比


---

## 🔍 性能分析总结

### 📊 关键发现

1. **最佳模型**: MobileNet 在所有指标上表现最优
2. **效率之王**: MobileNet 提供了最佳的性能/参数比
3. **稳定性**: 前3名模型的R²分数都在 -0.604 以上
4. **模型差异**: 最佳与最差模型R²差距为 0.766

### 💡 建议

- **生产部署**: 优先选择 MobileNet 或 ResNet
- **资源受限**: 推荐使用 MobileNet
- **实验研究**: 尝试 Transformer 类模型探索新方向
- **基线对比**: 使用 MLP 作为性能下限参考

### 🎯 性能等级分类


- **优秀级 (R² ≥ 0.75)**: 0 个模型
  - 

- **良好级 (0.65 ≤ R² < 0.75)**: 0 个模型
  - 无

- **基础级 (R² < 0.65)**: 4 个模型
  - MobileNet, ResNet, MLP, ViT

---

## 📝 技术说明

### 评估指标说明
- **R² Score**: 决定系数，衡量模型解释数据方差的比例 (0-1，越大越好)
- **RMSE**: 均方根误差，预测值与真实值的标准偏差 (越小越好)
- **MAE**: 平均绝对误差，预测偏差的平均值 (越小越好)
- **MAPE**: 平均绝对百分比误差，相对误差的百分比 (越小越好)

### 测试环境
- **硬件**: GTX 1050 / RTX 3080
- **软件**: PyTorch 1.12+, Python 3.8+
- **数据**: 沈阳市122个区域的街景图像
- **配置**: batch_size=4-8, epochs=50

---

*报告生成于 2025-06-02 17:59:38*
*项目地址: [GitHub Repository](https://github.com/your-repo)*
