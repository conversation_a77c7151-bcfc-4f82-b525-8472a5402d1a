"""
DenseNet模型
论文: Densely Connected Convolutional Networks
链接: https://arxiv.org/abs/1608.06993

DenseNet通过密集连接缓解梯度消失，加强特征传播，鼓励特征重用，大幅减少参数数量
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from collections import OrderedDict
import logging
import os
import sys
import json
from datetime import datetime
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import matplotlib.pyplot as plt
import numpy as np

logger = logging.getLogger(__name__)

class _DenseLayer(nn.Sequential):
    """DenseNet的基本层"""
    
    def __init__(self, num_input_features, growth_rate, bn_size, drop_rate):
        super(_DenseLayer, self).__init__()
        # BN-ReLU-Conv1x1-BN-ReLU-Conv3x3结构
        self.add_module('norm1', nn.BatchNorm2d(num_input_features))
        self.add_module('relu1', nn.ReLU(inplace=True))
        self.add_module('conv1', nn.Conv2d(num_input_features, bn_size * growth_rate,
                                          kernel_size=1, stride=1, bias=False))
        self.add_module('norm2', nn.BatchNorm2d(bn_size * growth_rate))
        self.add_module('relu2', nn.ReLU(inplace=True))
        self.add_module('conv2', nn.Conv2d(bn_size * growth_rate, growth_rate,
                                          kernel_size=3, stride=1, padding=1, bias=False))
        self.drop_rate = drop_rate

    def forward(self, x):
        # 保存输入用于密集连接
        features = super(_DenseLayer, self).forward(x)
        if self.drop_rate > 0:
            features = F.dropout(features, p=self.drop_rate, training=self.training)
        return torch.cat([x, features], 1)

class _DenseBlock(nn.Sequential):
    """DenseNet块 - 包含多个密集连接的层"""
    
    def __init__(self, num_layers, num_input_features, bn_size, growth_rate, drop_rate):
        super(_DenseBlock, self).__init__()
        for i in range(num_layers):
            layer = _DenseLayer(
                num_input_features + i * growth_rate,
                growth_rate, bn_size, drop_rate
            )
            self.add_module('denselayer%d' % (i + 1), layer)

class _Transition(nn.Sequential):
    """过渡层 - 用于降低特征图大小和通道数"""
    
    def __init__(self, num_input_features, num_output_features):
        super(_Transition, self).__init__()
        self.add_module('norm', nn.BatchNorm2d(num_input_features))
        self.add_module('relu', nn.ReLU(inplace=True))
        self.add_module('conv', nn.Conv2d(num_input_features, num_output_features,
                                          kernel_size=1, stride=1, bias=False))
        self.add_module('pool', nn.AvgPool2d(kernel_size=2, stride=2))

class DenseNetEnergyPredictor(nn.Module):
    """DenseNet能耗预测模型"""
    
    def __init__(self, growth_rate=32, block_config=(6, 12, 24, 16), num_init_features=64,
                 bn_size=4, drop_rate=0.2, num_classes=1000, output_dim=128, head_dropout=0.2):
        """
        Args:
            growth_rate: 每层产生的特征图数量
            block_config: 每个DenseBlock中的层数
            num_init_features: 第一个卷积层的输出通道数
            bn_size: 瓶颈层的倍数
            drop_rate: dropout率
            output_dim: 特征维度
            head_dropout: 预测头的dropout率
        """
        super(DenseNetEnergyPredictor, self).__init__()
        
        self.model_name = "DenseNet"
        
        # 第一个卷积层
        self.features = nn.Sequential(OrderedDict([
            ('conv0', nn.Conv2d(3, num_init_features, kernel_size=7, stride=2, padding=3, bias=False)),
            ('norm0', nn.BatchNorm2d(num_init_features)),
            ('relu0', nn.ReLU(inplace=True)),
            ('pool0', nn.MaxPool2d(kernel_size=3, stride=2, padding=1)),
        ]))

        # 构建DenseBlock
        num_features = num_init_features
        for i, num_layers in enumerate(block_config):
            block = _DenseBlock(
                num_layers=num_layers,
                num_input_features=num_features,
                bn_size=bn_size,
                growth_rate=growth_rate,
                drop_rate=drop_rate
            )
            self.features.add_module('denseblock%d' % (i + 1), block)
            num_features = num_features + num_layers * growth_rate
            
            if i != len(block_config) - 1:
                trans = _Transition(
                    num_input_features=num_features,
                    num_output_features=num_features // 2
                )
                self.features.add_module('transition%d' % (i + 1), trans)
                num_features = num_features // 2

        # 最终的批归一化
        self.features.add_module('norm5', nn.BatchNorm2d(num_features))
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool2d(1)
        
        # 特征投影
        self.feature_projector = nn.Sequential(
            nn.Linear(num_features, num_features // 2),
            nn.BatchNorm1d(num_features // 2),
            nn.ReLU(),
            nn.Dropout(head_dropout),
            nn.Linear(num_features // 2, output_dim),
            nn.ReLU()
        )
        
        # 回归头
        self.regression_head = nn.Sequential(
            nn.Linear(output_dim, 64),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(head_dropout),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1)
        )

        # 初始化权重
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        features = self.features(x)
        out = F.relu(features, inplace=True)
        out = self.global_pool(out)
        out = torch.flatten(out, 1)
        
        # 特征提取
        embeddings = self.feature_projector(out)
        
        # 回归预测
        output = self.regression_head(embeddings)
        output = output.squeeze(-1)
        
        return output, embeddings
    
    def count_parameters(self):
        total = sum(p.numel() for p in self.parameters())
        trainable = sum(p.numel() for p in self.parameters() if p.requires_grad)
        return total, trainable

class DenseNet121(DenseNetEnergyPredictor):
    """DenseNet-121配置"""
    def __init__(self, **kwargs):
        super().__init__(
            growth_rate=32,
            block_config=(6, 12, 24, 16),
            num_init_features=64,
            **kwargs
        )
        self.model_name = "DenseNet-121"

class DenseNet169(DenseNetEnergyPredictor):
    """DenseNet-169配置"""
    def __init__(self, **kwargs):
        super().__init__(
            growth_rate=32,
            block_config=(6, 12, 32, 32),
            num_init_features=64,
            **kwargs
        )
        self.model_name = "DenseNet-169"

class DenseNetCompact(DenseNetEnergyPredictor):
    """紧凑型DenseNet - 适合GTX 1050"""
    def __init__(self, **kwargs):
        super().__init__(
            growth_rate=16,  # 减少增长率
            block_config=(4, 8, 12, 8),  # 减少层数
            num_init_features=32,  # 减少初始特征
            bn_size=2,  # 减少瓶颈倍数
            **kwargs
        )
        self.model_name = "DenseNet-Compact"

class DenseNetMicro(DenseNetEnergyPredictor):
    """微型DenseNet - 超轻量级版本"""
    def __init__(self, **kwargs):
        super().__init__(
            growth_rate=12,
            block_config=(3, 6, 8, 6),
            num_init_features=24,
            bn_size=2,
            drop_rate=0.1,
            **kwargs
        )
        self.model_name = "DenseNet-Micro"

def create_densenet_model(model_size='compact', pretrained_backbone=False, **kwargs):
    """创建DenseNet模型"""
    
    if model_size == '121':
        model = DenseNet121(**kwargs)
    elif model_size == '169':
        model = DenseNet169(**kwargs)
    elif model_size == 'compact':
        model = DenseNetCompact(**kwargs)
    elif model_size == 'micro':
        model = DenseNetMicro(**kwargs)
    else:
        raise ValueError(f"不支持的模型大小: {model_size}")
    
    # 如果需要预训练权重
    if pretrained_backbone and model_size in ['121', '169']:
        # 这里可以加载torchvision的预训练权重
        # 注意只加载特征提取部分，不包括分类头
        pass
    
    return model

class DenseNetTrainer:
    """DenseNet训练器"""
    
    def __init__(self, model, device='cuda', learning_rate=0.001, weight_decay=1e-4):
        self.model = model.to(device)
        self.device = device
        self.criterion = nn.MSELoss()
        self.optimizer = torch.optim.Adam(
            model.parameters(), 
            lr=learning_rate, 
            weight_decay=weight_decay
        )
        self.scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, 
            mode='min', 
            factor=0.5, 
            patience=10
        )
        
        self.train_losses = []
        self.val_losses = []
        self.train_r2_scores = []
        self.val_r2_scores = []
    
    def train_epoch(self, train_loader):
        self.model.train()
        total_loss = 0
        predictions = []
        targets = []
        
        for batch_idx, batch in enumerate(train_loader):
            images = batch['image'].to(self.device)
            energy = batch['energy'].to(self.device)
            
            self.optimizer.zero_grad()
            outputs, _ = self.model(images)
            loss = self.criterion(outputs, energy)
            loss.backward()
            self.optimizer.step()
            
            total_loss += loss.item()
            predictions.extend(outputs.detach().cpu().numpy())
            targets.extend(energy.detach().cpu().numpy())
            
            if batch_idx % 10 == 0:
                logger.info(f'训练批次 {batch_idx}/{len(train_loader)}, 损失: {loss.item():.4f}')
        
        avg_loss = total_loss / len(train_loader)
        r2 = r2_score(targets, predictions)
        
        return avg_loss, r2
    
    def validate(self, val_loader):
        self.model.eval()
        total_loss = 0
        predictions = []
        targets = []
        
        with torch.no_grad():
            for batch in val_loader:
                images = batch['image'].to(self.device)
                energy = batch['energy'].to(self.device)
                
                outputs, _ = self.model(images)
                loss = self.criterion(outputs, energy)
                
                total_loss += loss.item()
                predictions.extend(outputs.cpu().numpy())
                targets.extend(energy.cpu().numpy())
        
        avg_loss = total_loss / len(val_loader)
        r2 = r2_score(targets, predictions)
        rmse = np.sqrt(mean_squared_error(targets, predictions))
        mae = mean_absolute_error(targets, predictions)
        
        return avg_loss, r2, rmse, mae, predictions, targets
    
    def train(self, train_loader, val_loader, epochs=50, save_dir='./results/saved_models', patience=15):
        os.makedirs(save_dir, exist_ok=True)
        best_val_loss = float('inf')
        best_model_path = None
        patience_counter = 0
        
        logger.info(f"开始训练DenseNet模型，总共 {epochs} 个epoch")
        
        for epoch in range(epochs):
            # 训练
            train_loss, train_r2 = self.train_epoch(train_loader)
            
            # 验证
            val_loss, val_r2, val_rmse, val_mae, _, _ = self.validate(val_loader)
            
            # 学习率调度
            self.scheduler.step(val_loss)
            
            # 记录历史
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.train_r2_scores.append(train_r2)
            self.val_r2_scores.append(val_r2)
            
            logger.info(f'Epoch {epoch+1}/{epochs}:')
            logger.info(f'  训练损失: {train_loss:.4f}, 训练R²: {train_r2:.4f}')
            logger.info(f'  验证损失: {val_loss:.4f}, 验证R²: {val_r2:.4f}')
            logger.info(f'  验证RMSE: {val_rmse:.4f}, 验证MAE: {val_mae:.4f}')
            
            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                best_model_path = os.path.join(save_dir, 'shenyang_DenseNet.pt')
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'val_loss': val_loss,
                    'val_r2': val_r2,
                    'config': {
                        'model_type': 'DenseNet',
                        'model_size': 'compact',
                        'output_dim': 128,
                        'dropout': 0.2
                    }
                }, best_model_path)
                logger.info(f'保存最佳模型: {best_model_path}')
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    logger.info(f'早停: 验证损失在 {patience} 个epoch内未改善')
                    break
        
        logger.info(f"训练完成! 最佳验证损失: {best_val_loss:.4f}")
        return best_model_path
    
    def plot_training_curves(self, save_path='densenet_training_history.png'):
        """绘制训练曲线"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # 损失曲线
        ax1.plot(self.train_losses, label='训练损失')
        ax1.plot(self.val_losses, label='验证损失')
        ax1.set_title('训练和验证损失')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True)
        
        # R²分数
        ax2.plot(self.train_r2_scores, label='训练R²')
        ax2.plot(self.val_r2_scores, label='验证R²')
        ax2.set_title('训练和验证R²分数')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('R² Score')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig(save_path)
        plt.close()
        logger.info(f"训练曲线保存至: {save_path}")

def analyze_densenet_efficiency(model):
    """分析DenseNet的效率"""
    total_params, trainable_params = model.count_parameters()
    
    # 分析每个DenseBlock的参数量
    block_params = {}
    for name, module in model.named_modules():
        if 'denseblock' in name and len(name.split('.')) == 2:
            block_params[name] = sum(p.numel() for p in module.parameters())
    
    # 计算特征重用率
    # DenseNet的一个关键优势是特征重用
    logger.info("\n=== DenseNet效率分析 ===")
    logger.info(f"总参数量: {total_params:,}")
    logger.info(f"可训练参数: {trainable_params:,}")
    
    for block_name, params in block_params.items():
        logger.info(f"{block_name}: {params:,} 参数 ({params/total_params*100:.1f}%)")
    
    # 计算内存效率
    # DenseNet虽然参数少，但需要保存所有中间特征图
    # 这里给出一个估算
    batch_size = 8
    input_size = 224
    growth_rate = model.features.denseblock1.denselayer1.conv2.out_channels
    
    logger.info(f"\n增长率: {growth_rate}")
    logger.info(f"特征重用带来的参数节省: 约{(1 - trainable_params/total_params)*100:.1f}%")
    
    return {
        'total_params': total_params,
        'trainable_params': trainable_params,
        'block_params': block_params
    }

def test_model(model_class, model_path, test_loader, device='cuda'):
    """测试模型"""
    # 加载模型
    checkpoint = torch.load(model_path, map_location=device)
    model = model_class()
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()
    
    predictions = []
    targets = []
    region_ids = []
    
    with torch.no_grad():
        for batch in test_loader:
            images = batch['image'].to(device)
            energy = batch['energy'].to(device)
            batch_region_ids = batch['region_id']
            
            outputs, _ = model(images)
            
            predictions.extend(outputs.cpu().numpy())
            targets.extend(energy.cpu().numpy())
            region_ids.extend(batch_region_ids.numpy() if torch.is_tensor(batch_region_ids) else batch_region_ids)
    
    # 计算指标
    r2 = r2_score(targets, predictions)
    rmse = np.sqrt(mean_squared_error(targets, predictions))
    mae = mean_absolute_error(targets, predictions)
    mape = np.mean(np.abs((np.array(targets) - np.array(predictions)) / np.array(targets))) * 100
    
    results = {
        'model_type': 'DenseNet',
        'r2_score': float(r2),  # 确保是Python float
        'rmse': float(rmse),
        'mae': float(mae),
        'mape': float(mape),
        'predictions': [float(x) for x in predictions],
        'targets': [float(x) for x in targets],
        'region_ids': [int(x) for x in region_ids]
    }
    
    logger.info("=== DenseNet模型测试结果 ===")
    logger.info(f"R² Score: {r2:.4f}")
    logger.info(f"RMSE: {rmse:.4f}")
    logger.info(f"MAE: {mae:.4f}")
    logger.info(f"MAPE: {mape:.2f}%")
    
    return results

def main():
    """测试DenseNet模型"""
    from data_loader import create_data_loaders, CONFIGS
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 创建模型 - 使用紧凑版本
    model = create_densenet_model(model_size='compact', output_dim=128, head_dropout=0.2)
    
    # 分析模型效率
    efficiency_stats = analyze_densenet_efficiency(model)
    
    # 测试前向传播
    dummy_input = torch.randn(2, 3, 224, 224).to(device)
    model = model.to(device)
    output, features = model(dummy_input)
    logger.info(f"输出形状: {output.shape}, 特征形状: {features.shape}")
    
    # 加载数据
    config = CONFIGS['shenyang']
    train_loader, val_loader, test_loader = create_data_loaders(
        config,
        batch_size=8,
        num_workers=0,
        max_images_per_region=3
    )
    
    # 创建训练器
    trainer = DenseNetTrainer(
        model,
        device=device,
        learning_rate=0.001,
        weight_decay=1e-4
    )
    
    # 训练模型
    best_model_path = trainer.train(
        train_loader,
        val_loader,
        epochs=50,
        save_dir='./results/saved_models',
        patience=15
    )
    
    # 绘制训练曲线
    trainer.plot_training_curves()
    
    # 测试模型
    if best_model_path:
        test_results = test_model(
            model_class=lambda: create_densenet_model(model_size='compact', output_dim=128),
            model_path=best_model_path,
            test_loader=test_loader,
            device=device
        )
        
        # 保存结果
        os.makedirs('results/test_results', exist_ok=True)
        with open('results/test_results/densenet_test_results.json', 'w') as f:
            json.dump(test_results, f, indent=2)
    
    # 比较不同DenseNet变体的效率
    logger.info("\n=== DenseNet变体比较 ===")
    variants = ['micro', 'compact', '121']
    for variant in variants:
        model_variant = create_densenet_model(model_size=variant)
        total, trainable = model_variant.count_parameters()
        logger.info(f"{model_variant.model_name}: {total:,} 参数 ({total/1e6:.1f}M)")

if __name__ == "__main__":
    main()